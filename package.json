{"name": "dexhero-extension", "version": "1.0.0", "description": "DexHero - Asset manager browser extension for tokenized avatars, games and items", "source": "./index.html", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:extension": "vite build --mode extension"}, "dependencies": {"@google/model-viewer": "^4.1.0", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "firebase": "^10.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "three": "^0.172.0", "uuid": "^9.0.1"}, "devDependencies": {"@animaapp/vite-plugin-screen-graph": "^0.1.5", "@types/chrome": "^0.0.254", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4", "vite-plugin-web-extension": "^4.1.1"}}