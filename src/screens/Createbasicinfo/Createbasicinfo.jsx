import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDexHero } from "../../contexts/DexHeroContext";

export const Createbasicinfo = () => {
  const { 
    creationData, 
    updateCreationData, 
    generateTicker, 
    validateBasicInfo,
    loadCreationDraft 
  } = useDexHero();

  const [formData, setFormData] = useState(creationData.basicInfo);
  const [showDropdown, setShowDropdown] = useState(false);

  // Load draft on component mount
  useEffect(() => {
    // Only load draft if loadCreationDraft function is available
    if (loadCreationDraft) {
      loadCreationDraft();
    }
  }, [loadCreationDraft]);

  // Update local state when context changes
  useEffect(() => {
    setFormData(creationData.basicInfo);
  }, [creationData.basicInfo]);

  // Handle form field changes
  const handleInputChange = (field, value) => {
    const updatedData = { ...formData, [field]: value };
    
    setFormData(updatedData);
    updateCreationData('basicInfo', updatedData);
    
    // Validate after update
    setTimeout(() => validateBasicInfo(), 100);
  };

  // Blockchain options
  const blockchainOptions = [
    { value: 'solana', label: 'Solana', icon: '◎' },
    { value: 'ethereum', label: 'Ethereum', icon: 'Ξ' },
    { value: 'polygon', label: 'Polygon', icon: '⬟' }
  ];

  const selectedBlockchain = blockchainOptions.find(opt => opt.value === formData.blockchain) || blockchainOptions[0];
  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full"
      data-model-id="1305:8226"
    >
      <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
          <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
            <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
              <div className="inline-flex items-center gap-[9.09px] relative">
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
              </div>
            </div>

            <div className="relative w-[100px] h-[21px]">
              <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                  0x5kb...vl86d
                </div>
              </div>
            </div>

            <img
              className="relative w-[29.7px] h-[29.7px]"
              alt="Profile"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </header>

          <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
            {/* Next Button */}
            <Link
              to="/create"
              className="absolute w-[57px] h-[21px] top-[7px] left-[618px] cursor-pointer z-10"
            >
              <div className="w-[41px] top-0 left-0 [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] leading-[20.5px] absolute tracking-[0] hover:text-white transition-colors">
                Next
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] right-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                style={{ transform: 'scaleX(-1)' }}
              />
            </Link>

            <div className="absolute w-[675px] h-[440px] top-0 left-0">
              <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-[0px_8px_8px_8px] border border-solid border-[#6a6f75]" />

              <div className="absolute w-[117px] h-8 top-0 left-0">
                <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                  <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />

                  <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                    Basic Info
                  </div>
                </div>
              </div>

              <div className="absolute top-[60px] left-[61px] [font-family:'Lato',Helvetica] font-light text-white text-xl tracking-[0] leading-[normal] whitespace-nowrap">
                Dexhero
              </div>

              <div className="absolute w-[253px] h-[35px] top-24 left-[61px]">
                <div className="relative w-[251px] h-[35px] rounded-[5.54px]">
                  <input
                    type="text"
                    name="dexhero-name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Name"
                    className="absolute w-[251px] h-[35px] top-0 left-0 rounded-[5.54px] border-[0.5px] border-solid border-white bg-transparent text-white text-[14.8px] font-light px-[13px] py-2 outline-none focus:border-[#08f7fe] transition-colors"
                    style={{ fontFamily: 'Lato, Helvetica' }}
                  />
                </div>
              </div>

              <div className="absolute top-[60px] left-[362px] [font-family:'Lato',Helvetica] font-light text-white text-xl tracking-[0] leading-[normal] whitespace-nowrap">
                Blockchain
              </div>

              <div className="absolute w-[251px] h-[35px] top-24 left-[363px]">
                <div className="relative">
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="w-[251px] h-[35px] rounded-[5.54px] border-[0.5px] border-solid border-white bg-transparent text-white text-[14.8px] font-light px-[13px] py-2 outline-none focus:border-[#08f7fe] transition-colors flex items-center justify-between"
                    style={{ fontFamily: 'Lato, Helvetica' }}
                  >
                    <span className="flex items-center gap-2">
                      <span>{selectedBlockchain.icon}</span>
                      <span>{selectedBlockchain.label}</span>
                    </span>
                    <img
                      className={`w-[17px] h-[11px] transition-transform ${showDropdown ? 'rotate-180' : ''}`}
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/vector-45.svg"
                    />
                  </button>
                  
                  {showDropdown && (
                    <div className="absolute top-[37px] left-0 w-[251px] bg-black border-[0.5px] border-solid border-white rounded-[5.54px] z-10">
                      {blockchainOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => {
                            handleInputChange('blockchain', option.value);
                            setShowDropdown(false);
                          }}
                          className="w-full px-[13px] py-2 text-left text-white text-[14.8px] font-light hover:bg-[#1a1a1a] flex items-center gap-2 first:rounded-t-[5.54px] last:rounded-b-[5.54px]"
                          style={{ fontFamily: 'Lato, Helvetica' }}
                        >
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="absolute w-[253px] h-[35px] top-[167px] left-[61px]">
                <div className="relative w-[251px] h-[35px] rounded-[5.54px]">
                  <input
                    type="text"
                    name="dexhero-collection"
                    value={formData.collection}
                    onChange={(e) => handleInputChange('collection', e.target.value)}
                    placeholder="Collection (optional)"
                    className="absolute w-[251px] h-[35px] top-0 left-0 rounded-[5.54px] border-[0.5px] border-solid border-white bg-transparent text-white text-[14.8px] font-light px-[13px] py-2 outline-none focus:border-[#08f7fe] transition-colors"
                    style={{ fontFamily: 'Lato, Helvetica' }}
                  />
                </div>
              </div>

              <div className="absolute w-[253px] h-[35px] top-[167px] left-[362px]">
                <div className="relative w-[251px] h-[35px] rounded-[5.54px]">
                  <input
                    type="text"
                    name="dexhero-ticker"
                    value={formData.ticker}
                    onChange={(e) => handleInputChange('ticker', e.target.value.toUpperCase())}
                    placeholder="Ticker"
                    maxLength="6"
                    className="absolute w-[251px] h-[35px] top-0 left-0 rounded-[5.54px] border-[0.5px] border-solid border-white bg-transparent text-white text-[14.8px] font-light px-[13px] py-2 outline-none focus:border-[#08f7fe] transition-colors uppercase"
                    style={{ fontFamily: 'Lato, Helvetica' }}
                  />
                </div>
              </div>

              <div className="absolute top-[232px] left-[46px] [font-family:'Lato',Helvetica] font-light text-white text-[14.8px] tracking-[0] leading-[normal]">
                Description/lore
              </div>

              <div className="absolute w-[583px] h-[142px] top-[260px] left-[46px]">
                <textarea
                  name="dexhero-description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Provide A Detailed Description Of Your Item"
                  className="w-[583px] h-[142px] rounded-[5.52px] border-[0.5px] border-solid border-white bg-transparent text-white text-[14.8px] font-light px-[14px] py-3 outline-none focus:border-[#08f7fe] transition-colors resize-none"
                  style={{ fontFamily: 'Lato, Helvetica' }}
                  maxLength="500"
                />
                <div className="absolute bottom-2 right-3 text-[#a48ea9] text-[10px]">
                  {formData.description.length}/500
                </div>
              </div>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[114px] block"
                to="/create"
              >
                <div className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  3D Model
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[228px] block"
                to="/createanimations"
              >
                <div className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  Animations
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[342px] block"
                to="/reviewdexhero"
              >
                <div className="absolute top-[7px] left-2 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[13px] tracking-[0] leading-[normal]">
                  Review DexHero
                </div>
              </Link>
            </div>
          </div>
        </div>

        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />

          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>

          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>

          <img
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
            alt="Createpage"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />

          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>

          <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>

          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>
      </div>
    </div>
  );
};
