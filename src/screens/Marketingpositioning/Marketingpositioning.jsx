import React, { useEffect, useRef, useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useDexHero } from "../../contexts/DexHeroContext";
import '@google/model-viewer';

export const Marketingpositioning = () => {
  const navigate = useNavigate();
  const { creationData, setCreationStep } = useDexHero();
  const nameRef = useRef(null);
  const [optimalFontSize, setOptimalFontSize] = useState(79.5);
  
  // State for minting process
  const [showMintingPopup, setShowMintingPopup] = useState(false);
  const [tokenPurchaseAmount, setTokenPurchaseAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [allConfirmed, setAllConfirmed] = useState(false);
  
  // Extract data from creation context
  const dexheroName = creationData.basicInfo.name || "DexHero Name";
  const dexheroTicker = creationData.basicInfo.ticker || "HERO";
  const dexheroDescription = creationData.basicInfo.description || "Your DexHero description will appear here";
  const modelUrl = creationData.modelUrl;


  // If no model file is uploaded, show the animations-page-style layout
  if (!creationData.modelFile) {
    return (
      <div
        className="bg-transparent flex flex-row justify-center w-full"
        data-model-id="1305:16763"
      >
        <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
          <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
            <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
              <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
                <div className="inline-flex items-center gap-[9.09px] relative">
                  <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </div>
                  <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </div>
                  <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </div>
                  <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </div>
                  <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative w-[100px] h-[21px]">
                <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                  <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                    0x5kb...vl86d
                  </div>
                </div>
              </div>
              <img
                className="relative w-[29.7px] h-[29.7px]"
                alt="Profile"
                src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
              />
            </header>

            <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
              <div className="absolute w-[57px] h-[21px] top-[7px] left-[604px]">
                <div className="absolute w-[41px] top-0 left-[15px] [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] tracking-[0] leading-[20.5px]">
                  Back
                </div>
                <img
                  className="absolute w-[7px] h-[11px] top-[5px] left-0"
                  alt="Vector"
                  src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                />
              </div>

              <div className="absolute w-[675px] h-[440px] top-0 left-0">
                <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

                {/* No Model Uploaded Message */}
                <div className="absolute w-[640px] h-[370px] top-[50px] left-[17px]">
                  <div className="w-full h-full flex items-center justify-center border-2 border-dashed border-[#3a3a3a] rounded-lg">
                    <div className="text-center">
                      <p className="[font-family:'Lato',Helvetica] font-medium text-[#8d8d8d] text-lg mb-2">
                        No 3D Model Uploaded
                      </p>
                      <p className="[font-family:'Lato',Helvetica] font-light text-[#5a5a5a] text-sm">
                        Please upload a 3D model first for marketing
                      </p>
                    </div>
                  </div>
                </div>

                {/* Tab Navigation - Marketing tab is active */}
                <div className="absolute w-[117px] h-8 top-0 left-[342px]">
                  <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                    <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />
                    <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                      Marketing
                    </div>
                  </div>
                </div>

                <div className="absolute w-[117px] h-8 top-0 left-0">
                  <div 
                    className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors cursor-pointer"
                    onClick={() => navigate('/createbasicinfo')}
                  >
                    Basic Info
                  </div>
                </div>

                <div className="absolute w-[117px] h-8 top-0 left-[114px]">
                  <div 
                    className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors cursor-pointer"
                    onClick={() => navigate('/create3dmodel')}
                  >
                    3D Model
                  </div>
                </div>

                <div className="absolute w-[117px] h-8 top-0 left-[228px]">
                  <div 
                    className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors cursor-pointer"
                    onClick={() => navigate('/createanimations')}
                  >
                    Animations
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
            <img
              className="absolute w-[33px] h-px top-10 left-[5px]"
              alt="Dividingline"
              src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
            />
            
            <Link to="/hometoken">
              <img
                className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
                alt="Tokenpage"
                src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
              />
            </Link>
            
            <Link to="/homegamesimg">
              <img
                className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
                alt="Gamespage"
                src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
              />
            </Link>
            
            <img
              className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
              alt="Createpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
            
            <Link
              className="absolute w-5 h-5 top-[122px] left-[11px] block"
              to="/homeitemimg"
            >
              <img
                className="absolute w-[9px] h-[9px] top-0 left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-0 left-3"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-3 left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-3 left-3"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
              />
            </Link>
            
            <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
              <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
            </div>
            
            <img
              className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
              alt="Dexherologo"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </div>
        </div>
      </div>
    );
  }
  
  // Refs for confirmation tour elements
  const headerTickerRef = useRef(null);
  const mainNameRef = useRef(null);
  const descriptionRef = useRef(null);
  const mainModelRef = useRef(null);
  const gameSlotRef = useRef(null);
  const containerRef = useRef(null);
  
  // Generate mock 24hr gain for preview
  const mockGain = "+55%";

  // Smart text fitting function that returns both font size and formatted text
  const calculateOptimalDisplay = (text, maxWidth = 214, maxHeight = 167) => {
    if (!text) return { fontSize: 12, formattedText: text.toUpperCase() };

    const upperText = text.toUpperCase();
    
    // Create a temporary element to measure text
    const tempElement = document.createElement('div');
    tempElement.style.position = 'absolute';
    tempElement.style.visibility = 'hidden';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.fontFamily = 'Vanguard_CF-DemiBold, Helvetica';
    tempElement.style.fontWeight = 'bold';
    tempElement.style.padding = '0';
    tempElement.style.margin = '0';
    tempElement.style.border = 'none';
    document.body.appendChild(tempElement);

    let bestFontSize = 12;
    let bestFormattedText = upperText;
    
    // Try different font sizes from large to small
    for (let fontSize = 79.5; fontSize >= 12; fontSize -= 0.5) {
      const lineHeight = fontSize * 0.937;
      
      tempElement.style.fontSize = `${fontSize}px`;
      tempElement.style.lineHeight = `${lineHeight}px`;
      
      // First check if it fits in a single line
      tempElement.textContent = upperText;
      const singleLineWidth = tempElement.offsetWidth;
      
      if (singleLineWidth <= maxWidth && lineHeight <= maxHeight) {
        // Single line fits perfectly
        bestFontSize = fontSize;
        bestFormattedText = upperText;
        break;
      }
      
      // Try multi-line layout
      const words = upperText.split(' ');
      if (words.length > 1) {
        let lines = [];
        let currentLine = '';
        let allWordsFit = true;
        
        for (const word of words) {
          // Check if this word can fit on its own (minimum requirement)
          tempElement.textContent = word;
          if (tempElement.offsetWidth > maxWidth) {
            allWordsFit = false;
            break;
          }
          
          const testLine = currentLine ? `${currentLine} ${word}` : word;
          tempElement.textContent = testLine;
          
          if (tempElement.offsetWidth <= maxWidth) {
            currentLine = testLine;
          } else {
            if (currentLine) {
              lines.push(currentLine);
              currentLine = word;
            } else {
              allWordsFit = false;
              break;
            }
          }
        }
        
        if (currentLine && allWordsFit) {
          lines.push(currentLine);
        }
        
        if (allWordsFit && lines.length > 0) {
          const totalHeight = lines.length * lineHeight;
          
          if (totalHeight <= maxHeight) {
            // Verify all lines fit within width
            let allLinesFit = true;
            for (const line of lines) {
              tempElement.textContent = line;
              if (tempElement.offsetWidth > maxWidth) {
                allLinesFit = false;
                break;
              }
            }
            
            if (allLinesFit) {
              bestFontSize = fontSize;
              bestFormattedText = lines.join('\n');
              break;
            }
          }
        }
      }
    }

    document.body.removeChild(tempElement);
    return { 
      fontSize: bestFontSize, 
      formattedText: bestFormattedText 
    };
  };

  // State for formatted text and popup
  const [formattedText, setFormattedText] = useState(dexheroName.toUpperCase());
  const [showDescriptionPopup, setShowDescriptionPopup] = useState(false);
  
  // Confirmation tour state
  const [confirmationStep, setConfirmationStep] = useState(0);
  const [showConfirmationTour, setShowConfirmationTour] = useState(false);
  const [confirmedSpots, setConfirmedSpots] = useState({
    headerTicker: false,
    mainName: false,
    description: false,
    mainModel: false,
    gameSlot: false
  });

  // Function to get element position relative to container
  const getElementPosition = (elementRef, containerRef) => {
    if (!elementRef.current || !containerRef.current) {
      return { top: '0px', left: '0px', width: '0px', height: '0px' };
    }
    
    const elementRect = elementRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    return {
      top: `${elementRect.top - containerRect.top}px`,
      left: `${elementRect.left - containerRect.left}px`,
      width: `${elementRect.width}px`,
      height: `${elementRect.height}px`
    };
  };

  // Get current element position for the active step
  const getCurrentStepPosition = () => {
    const refs = [headerTickerRef, mainNameRef, descriptionRef, mainModelRef, gameSlotRef];
    const containerElement = document.querySelector('.overflow-hidden.w-\\[751\\.76px\\].h-\\[508px\\]');
    
    if (!refs[confirmationStep]?.current || !containerElement) {
      console.log('Missing ref or container:', { 
        refExists: !!refs[confirmationStep]?.current, 
        containerExists: !!containerElement,
        step: confirmationStep 
      });
      return { top: '0px', left: '0px', width: '0px', height: '0px' };
    }
    
    const element = refs[confirmationStep].current;
    const elementRect = element.getBoundingClientRect();
    const containerRect = containerElement.getBoundingClientRect();
    
    // Calculate position relative to the main container
    let adjustedTop = elementRect.top - containerRect.top;
    let adjustedLeft = elementRect.left - containerRect.left;
    
    // For debugging - log detailed positioning information
    console.log(`Step ${confirmationStep} positioning:`, {
      stepName: ['headerTicker', 'mainName', 'description', 'mainModel', 'gameSlot'][confirmationStep],
      elementRect: {
        top: elementRect.top,
        left: elementRect.left,
        width: elementRect.width,
        height: elementRect.height,
        bottom: elementRect.bottom,
        right: elementRect.right
      },
      containerRect: {
        top: containerRect.top,
        left: containerRect.left,
        width: containerRect.width,
        height: containerRect.height
      },
      adjustedPosition: {
        top: adjustedTop,
        left: adjustedLeft,
        width: elementRect.width,
        height: elementRect.height
      },
      elementId: element.id,
      elementClasses: element.className
    });
    
    // Ensure we have valid positioning values
    adjustedTop = Math.max(0, adjustedTop);
    adjustedLeft = Math.max(0, adjustedLeft);
    
    return {
      top: `${adjustedTop}px`,
      left: `${adjustedLeft}px`,
      width: `${elementRect.width}px`,
      height: `${elementRect.height}px`
    };
  };

  // Confirmation tour steps with dynamic positioning
  const confirmationSteps = [
    {
      id: 'headerTicker',
      title: 'Header Ticker Display',
      description: `This shows how your DexHero "${dexheroTicker}" will appear in the header ticker with 24hr gains and your 3D model.`,
      ref: headerTickerRef,
      target: 'header-ticker'
    },
    {
      id: 'mainName',
      title: 'DexHero Name Display',
      description: `This is how your DexHero name "${dexheroName}" will be displayed on the homepage with optimized sizing.`,
      ref: mainNameRef,
      target: 'main-name'
    },
    {
      id: 'description',
      title: 'Description Preview',
      description: 'This shows how your description will appear. Click it to see the full text in a popup.',
      ref: descriptionRef,
      target: 'description-area'
    },
    {
      id: 'mainModel',
      title: 'Main Model Display',
      description: 'This is how your 3D model will be showcased on the homepage next to your name and description.',
      ref: mainModelRef,
      target: 'main-model'
    },
    {
      id: 'gameSlot',
      title: 'Games Section Display',
      description: 'This shows how your DexHero will appear in the games section alongside other content.',
      ref: gameSlotRef,
      target: 'game-slot'
    }
  ];

  // Start confirmation tour
  const startConfirmationTour = () => {
    setShowConfirmationTour(true);
    setConfirmationStep(0);
  };

  // Handle confirmation
  const handleConfirmSpot = (spotId) => {
    const newConfirmedSpots = { ...confirmedSpots, [spotId]: true };
    setConfirmedSpots(newConfirmedSpots);
    
    if (confirmationStep < confirmationSteps.length - 1) {
      setConfirmationStep(prev => prev + 1);
    } else {
      // All spots confirmed
      setShowConfirmationTour(false);
      // Check if all spots are confirmed
      const allSpotsConfirmed = Object.values(newConfirmedSpots).every(confirmed => confirmed);
      setAllConfirmed(allSpotsConfirmed);
    }
  };

  // Skip current spot
  const handleSkipSpot = () => {
    if (confirmationStep < confirmationSteps.length - 1) {
      setConfirmationStep(prev => prev + 1);
    } else {
      setShowConfirmationTour(false);
    }
  };

  // State to track when scrolling is complete
  const [isScrolling, setIsScrolling] = useState(false);

  // Auto-scroll to element when confirmation step changes
  useEffect(() => {
    if (showConfirmationTour && confirmationSteps[confirmationStep]) {
      const targetRef = confirmationSteps[confirmationStep].ref;
      if (targetRef?.current) {
        // Find the scrollable container
        const scrollContainer = document.querySelector('.overflow-y-scroll');
        if (scrollContainer) {
          const elementRect = targetRef.current.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          
          // Calculate if element is visible in the container
          const elementTop = elementRect.top - containerRect.top;
          const elementBottom = elementRect.bottom - containerRect.top;
          const containerHeight = containerRect.height;
          
          // If element is not fully visible, scroll to it
          if (elementTop < 0 || elementBottom > containerHeight) {
            setIsScrolling(true);
            const scrollTop = scrollContainer.scrollTop;
            const targetScrollTop = scrollTop + elementTop - containerHeight / 2 + elementRect.height / 2;
            
            scrollContainer.scrollTo({
              top: Math.max(0, targetScrollTop),
              behavior: 'smooth'
            });
            
            // Wait for scroll to complete before recalculating positions
            setTimeout(() => {
              setIsScrolling(false);
            }, 500); // 500ms should be enough for smooth scroll to complete
          }
        }
      }
    }
  }, [confirmationStep, showConfirmationTour]);

  // Recalculate font size and formatting when name changes
  useEffect(() => {
    const { fontSize: newFontSize, formattedText: newFormattedText } = calculateOptimalDisplay(dexheroName);
    setOptimalFontSize(newFontSize);
    setFormattedText(newFormattedText);
  }, [dexheroName]);
  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full relative"
      data-model-id="1343:13676"
    >
      <div className="overflow-hidden w-[751.76px] h-[508px] relative">
        {/* Start Confirmation Tour Button / Mint DexHero Button - Positioned within the 751.76px x 508px screen */}
        {!showConfirmationTour && (
          <button
            onClick={allConfirmed ? () => setShowMintingPopup(true) : startConfirmationTour}
            className="absolute bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg z-40 transition-colors text-sm"
          >
            {allConfirmed ? 'Mint DexHero' : 'Review Marketing Placement'}
          </button>
        )}

        {/* Confirmation Tour Overlay - Positioned within the 751.76px x 508px screen */}
        {showConfirmationTour && (
          <div className="absolute inset-0 z-50">
            {/* Create cutout effect using multiple overlays */}
            {/* Top overlay */}
            <div 
              className="absolute bg-black bg-opacity-70 pointer-events-none"
              style={{
                top: 0,
                left: 0,
                width: '100%',
                height: getCurrentStepPosition().top
              }}
            />
            
            {/* Bottom overlay */}
            <div 
              className="absolute bg-black bg-opacity-70 pointer-events-none"
              style={{
                top: `calc(${getCurrentStepPosition().top} + ${getCurrentStepPosition().height})`,
                left: 0,
                width: '100%',
                height: `calc(100% - ${getCurrentStepPosition().top} - ${getCurrentStepPosition().height})`
              }}
            />
            
            {/* Left overlay */}
            <div 
              className="absolute bg-black bg-opacity-70 pointer-events-none"
              style={{
                top: getCurrentStepPosition().top,
                left: 0,
                width: getCurrentStepPosition().left,
                height: getCurrentStepPosition().height
              }}
            />
            
            {/* Right overlay */}
            <div 
              className="absolute bg-black bg-opacity-70 pointer-events-none"
              style={{
                top: getCurrentStepPosition().top,
                left: `calc(${getCurrentStepPosition().left} + ${getCurrentStepPosition().width})`,
                width: `calc(100% - ${getCurrentStepPosition().left} - ${getCurrentStepPosition().width})`,
                height: getCurrentStepPosition().height
              }}
            />
            
            {/* Border highlight for current target */}
            <div 
              className="absolute border-2 border-blue-400 rounded-lg pointer-events-none"
              style={getCurrentStepPosition()}
            />
            
            {/* Confirmation Dialog - positioned to avoid cutoff */}
            <div 
              className="absolute bg-gray-900 border border-gray-600 rounded-lg p-4 shadow-xl z-60"
              style={{
                top: Math.min(
                  parseInt(getCurrentStepPosition().top) + parseInt(getCurrentStepPosition().height) + 10,
                  508 - 120 // Ensure it stays within the 508px height
                ),
                left: Math.max(10, Math.min(
                  parseInt(getCurrentStepPosition().left) + parseInt(getCurrentStepPosition().width)/2 - 140,
                  751.76 - 290 // Ensure it stays within the 751.76px width
                )),
                width: '280px'
              }}
            >
              <div className="mb-3">
                <h3 className="text-white text-sm font-semibold mb-1">
                  {confirmationSteps[confirmationStep].title}
                </h3>
                <p className="text-gray-300 text-xs leading-relaxed">
                  {confirmationSteps[confirmationStep].description}
                </p>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="text-gray-400 text-xs">
                  Step {confirmationStep + 1} of {confirmationSteps.length}
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={handleSkipSpot}
                    className="px-2 py-1 text-gray-400 hover:text-white text-xs transition-colors"
                  >
                    Skip
                  </button>
                  <button
                    onClick={() => handleConfirmSpot(confirmationSteps[confirmationStep].id)}
                    className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  >
                    {confirmationStep === confirmationSteps.length - 1 ? 'Finish' : 'Confirm'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />

          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>

          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>

          <img
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
            alt="Createpage"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />

          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>

          <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>

          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>

        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] overflow-y-scroll">
          <div className="relative w-[752px] h-[1858px] left-[-43px]">
            <div className="absolute w-[709px] h-[1858px] top-0 left-[43px] bg-black">
              <div className="absolute w-[438px] h-[300px] top-[87px] left-10">
                {modelUrl ? (
                  <div 
                    ref={mainModelRef}
                    id="main-model-area"
                    className="absolute w-[259px] h-[293px] top-px left-[179px] rounded overflow-hidden"
                  >
                    <model-viewer
                      src={modelUrl}
                      alt="DexHero Model"
                      style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'transparent'
                      }}
                      camera-orbit="45deg 75deg 105%"
                      disable-zoom
                      disable-pan
                      auto-rotate
                      auto-rotate-delay="1000"
                      rotation-per-second="15deg"
                    />
                  </div>
                ) : (
                  <img
                    id="main-model-area"
                    className="absolute w-[259px] h-[293px] top-px left-[179px] object-cover"
                    alt="Modelview"
                    src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                  />
                )}

                <div 
                  ref={mainNameRef}
                  id="main-name-area"
                  className="absolute w-[214px] h-[167px] top-0 left-0 flex items-center justify-start overflow-hidden"
                >
                  <div 
                    ref={nameRef}
                    className="[font-family:'Vanguard_CF-DemiBold',Helvetica] font-bold text-gray-50 tracking-[0] leading-tight w-full"
                    style={{
                      fontSize: `${optimalFontSize}px`,
                      lineHeight: `${optimalFontSize * 0.937}px`,
                      whiteSpace: 'pre-line',
                      wordBreak: 'keep-all',
                      overflowWrap: 'normal'
                    }}
                  >
                    {formattedText}
                  </div>
                </div>

                <div 
                  ref={descriptionRef}
                  id="description-area"
                  className="absolute w-[191px] h-[54px] top-[172px] left-0 overflow-hidden cursor-pointer"
                  onClick={() => setShowDescriptionPopup(true)}
                >
                  <p 
                    className="[font-family:'Inter',Helvetica] font-normal text-[#cfd4dc] text-[12px] tracking-[0] leading-[16px] m-0 p-0"
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {dexheroDescription}
                  </p>
                </div>

                <div className="absolute w-[39px] h-[39px] top-[234px] left-[21px]">
                  <div className="relative h-[39px] rounded-[19.44px]">
                    <img
                      className="absolute w-3.5 h-3.5 top-3 left-3"
                      alt="Icon"
                      src="https://c.animaapp.com/Hxjzzvpo/img/icon-10.svg"
                    />

                    <div className="absolute w-[39px] h-[39px] top-0 left-0 rounded-[19.44px] border-[0.97px] border-solid border-white" />
                  </div>
                </div>

                <div className="absolute w-[39px] h-[39px] top-[234px] left-[100px]">
                  <div className="relative h-[39px] rounded-[19.44px]">
                    <img
                      className="absolute w-3.5 h-3.5 top-3 left-3"
                      alt="Icon"
                      src="https://c.animaapp.com/Hxjzzvpo/img/icon-11.svg"
                    />

                    <div className="w-[39px] h-[39px] rounded-[19.44px] border-[0.97px] border-solid absolute top-0 left-0 border-white rotate-180" />
                  </div>
                </div>
              </div>

              <img
                className="absolute w-[89px] h-4 top-[59px] left-10"
                alt="Collectionname"
                src="https://c.animaapp.com/Hxjzzvpo/img/collectionname-5.svg"
              />

              <div className="absolute w-[687px] h-[1350px] top-[396px] left-[11px]">
                <div className="relative w-[675px] h-[1350px]">
                  <div className="absolute top-0.5 left-[604px] [font-family:'Asap',Helvetica] font-bold text-white text-xs text-center tracking-[0] leading-[20.6px] whitespace-nowrap">
                    CREATE
                  </div>

                  <div className="absolute w-[675px] h-[1350px] top-0 left-0">
                    <div className="absolute w-[117px] h-8 top-0 left-[114px]">
                      <div className="absolute top-[7px] left-9 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                        Items
                      </div>
                    </div>

                    <div className="absolute w-[117px] h-8 top-0 left-[228px]">
                      <div className="absolute top-[7px] left-9 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                        Token
                      </div>
                    </div>

                    <div className="absolute w-[675px] h-[1319px] top-[31px] left-0 rounded-[0px_8px_8px_8px] border border-solid border-[#6a6f75]" />

                    <div className="absolute top-[43px] left-[247px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10px] text-center tracking-[0] leading-[14.0px] whitespace-nowrap">
                      Volume
                    </div>

                    <div className="absolute top-[43px] left-[292px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10px] text-center tracking-[0] leading-[14.0px] whitespace-nowrap">
                      Txns
                    </div>

                    <div className="absolute top-[43px] left-[325px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10px] text-center tracking-[0] leading-[14.0px] whitespace-nowrap">
                      Mints
                    </div>

                    <div className="absolute top-[43px] left-[362px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10px] text-center tracking-[0] leading-[14.0px] whitespace-nowrap">
                      Gainers
                    </div>

                    <div className="absolute w-20 h-[15px] top-[42px] left-[551px]">
                      <div className="absolute top-0 left-[18px] [font-family:'Asap',Helvetica] font-normal text-white text-[12.7px] tracking-[1.33px] leading-[normal] whitespace-nowrap">
                        My Items
                      </div>

                      <div className="absolute w-3.5 h-3.5 top-0 left-0 bg-[#1a1c23] border-[1.27px] border-solid border-[#0074e080]" />
                    </div>

                    <div className="absolute w-[60px] h-[15px] top-[43px] left-[448px]">
                      <div className="absolute top-0 left-[19px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10.8px] text-center tracking-[0] leading-[15.1px] whitespace-nowrap">
                        Newest
                      </div>

                      <div className="absolute w-[15px] h-[15px] top-0 left-0 bg-[#d9d9d9] rounded-[7.5px]">
                        <img
                          className="absolute w-[9px] h-[11px] top-0.5 left-[3px]"
                          alt="H"
                          src="https://c.animaapp.com/Hxjzzvpo/img/h-4.svg"
                        />
                      </div>
                    </div>

                    <div className="absolute w-[77px] h-3.5 top-[43px] left-[18px]">
                      <div className="absolute top-0 left-0 [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10px] text-center tracking-[0] leading-[14.0px] whitespace-nowrap">
                        Last 24 hours
                      </div>

                      <img
                        className="absolute w-[9px] h-1.5 top-[5px] left-[66px]"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-135.svg"
                      />
                    </div>

                    <div className="absolute w-9 h-[15px] top-[42px] left-[202px]">
                      <div className="absolute top-0 left-[15px] [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10.5px] text-center tracking-[0] leading-[14.7px] whitespace-nowrap">
                        Top
                      </div>

                      <img
                        className="w-0.5 top-[5px] left-[9px] absolute h-[7px]"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-3-2.svg"
                      />

                      <img
                        className="w-0.5 h-[5px] top-1.5 absolute left-1"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-4-1.svg"
                      />

                      <img
                        className="absolute w-0.5 h-1 top-[7px] left-0"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-5-1.svg"
                      />
                    </div>

                    <div className="absolute w-[60px] h-[15px] top-[42px] left-[118px]">
                      <div className="absolute top-0 left-3.5 [font-family:'Manrope',Helvetica] font-medium text-blackampwhitewhite text-[10.5px] text-center tracking-[0] leading-[14.7px] whitespace-nowrap">
                        Trending
                      </div>

                      <div className="absolute w-2.5 h-2.5 top-[3px] left-0">
                        <img
                          className="w-[7px] top-0 left-0.5 absolute h-[7px]"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-3-3.svg"
                        />

                        <img
                          className="absolute w-px h-px top-0.5 left-[7px]"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-5-2.svg"
                        />

                        <img
                          className="absolute w-1 h-[3px] top-0.5 left-0"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-48.svg"
                        />

                        <img
                          className="w-[3px] h-[3px] top-[3px] absolute left-1"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-4-2.svg"
                        />

                        <img
                          className="absolute w-[3px] h-1 top-1.5 left-1"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-2-2.svg"
                        />

                        <img
                          className="absolute w-[3px] h-[3px] top-[7px] left-0"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/vector-6-2.svg"
                        />
                      </div>
                    </div>

                    <div className="absolute w-4 h-[13px] top-[43px] left-[646px]">
                      <div className="absolute w-4 h-[3px] top-0 left-0 bg-[#ffffff80]" />

                      <div className="absolute w-4 h-[3px] top-[5px] left-0 bg-[#ffffff80]" />

                      <div className="absolute w-4 h-[3px] top-2.5 left-0 bg-[#ffffff80]" />
                    </div>

                    {modelUrl ? (
                      <div 
                        ref={gameSlotRef}
                        id="game-slot-area"
                        className="absolute w-[122px] h-[149px] top-[69px] left-2.5 rounded overflow-hidden bg-gray-900"
                      >
                        <model-viewer
                          src={modelUrl}
                          alt="DexHero Model"
                          style={{
                            width: '100%',
                            height: '100%',
                            backgroundColor: 'transparent'
                          }}
                          camera-orbit="0deg 75deg 105%"
                          disable-zoom
                          disable-pan
                          auto-rotate
                          auto-rotate-delay="2000"
                          rotation-per-second="20deg"
                        />
                      </div>
                    ) : (
                      <img
                        id="game-slot-area"
                        className="absolute w-[122px] h-[149px] top-[69px] left-2.5 object-cover"
                        alt="Gameslot"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    )}

                    <img
                      className="absolute w-[122px] h-[149px] top-[69px] left-[143px]"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[69px] left-[276px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[69px] left-[410px]"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[69px] left-[543px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[230px] left-2.5 object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[230px] left-[143px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[230px] left-[276px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[230px] left-[410px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[230px] left-[543px] object-cover"
                      alt="Gameslot"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[389px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[389px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[389px] left-[276px]"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[389px] left-[410px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[389px] left-[543px]"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[548px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[548px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[548px] left-[276px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[548px] left-[410px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[548px] left-[543px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[707px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[707px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[707px] left-[276px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[707px] left-[410px]"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[707px] left-[543px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[868px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[868px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[868px] left-[276px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[868px] left-[410px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[148px] top-[868px] left-[543px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1027px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1027px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1027px] left-[276px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1027px] left-[410px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1027px] left-[543px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1187px] left-2.5 object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1187px] left-[143px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[122px] h-[149px] top-[1187px] left-[276px] object-cover"
                      alt="Rectangle"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />

                    <img
                      className="absolute w-[58px] h-[58px] top-[1219px] left-[441px]"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/vector-49.svg"
                    />

                    <div className="absolute w-[81px] top-[1285px] left-[430px] [font-family:'Asap',Helvetica] font-bold text-[#0074e0] text-[14.5px] text-center tracking-[0] leading-[24.9px]">
                      ADD GAME
                    </div>

                    <div className="absolute w-[117px] h-8 top-0 left-0">
                      <div className="relative w-[115px] h-8 bg-black rounded-[8px_8px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#6a6f75]">
                        <div className="absolute top-1.5 left-[30px] [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                          Games
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute w-[194px] top-[117px] left-[488px] [font-family:'Inter',Helvetica] font-semibold text-warning-500 text-xs tracking-[0] leading-[11.9px]">
                DexHero’s
              </div>

              <div className="absolute w-[194px] top-[138px] left-[488px] [font-family:'Bakbak_One',Helvetica] font-normal text-gray-50 text-[23.9px] tracking-[-0.48px] leading-[normal]">
                Playable NFT’s
              </div>

              <p className="absolute w-[175px] top-[180px] left-[488px] [font-family:'Inter',Helvetica] font-normal text-[#cfd4dc] text-[8.9px] tracking-[0] leading-[14.9px]">
                The wait is over! Welcome to the center of your web3 gaming
                experience!
              </p>

              <div className="absolute w-[58px] h-[72px] top-[255px] left-[562px]">
                <div className="w-14 top-[46px] left-0 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[12.8px] text-center leading-[11.5px] absolute tracking-[0]">
                  Build
                </div>

                <div className="absolute w-[33px] h-[33px] top-0 left-[11px] rounded-[16.5px]">
                  <div className="w-[33px] h-[33px] rounded-[16.5px] border-[0.82px] border-solid absolute top-0 left-0 border-white rotate-180" />

                  <img
                    className="absolute w-[15px] h-4 top-2 left-[9px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-50.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-0.5 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-75.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-75.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-3 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-16.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-16.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-[23px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-17.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-17.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-[33px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-18.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-18.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-11 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-19.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-19.svg"
                  />
                </div>
              </div>

              <div className="absolute w-[41px] h-[55px] top-[272px] left-[620px]">
                <div className="top-[35px] left-1.5 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[9.8px] text-center leading-[8.7px] whitespace-nowrap absolute tracking-[0]">
                  Trade
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-0 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-80.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-80.svg"
                  />
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-2 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-81.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-81.svg"
                  />
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-4 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-82.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-82.svg"
                  />
                </div>

                <div className="absolute w-[15px] h-2 top-[47px] left-[23px]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star background"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-background-83.svg"
                  />

                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-83.svg"
                  />

                  <img
                    className="w-2 h-2 left-2 absolute top-0"
                    alt="Star background"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-background-84.svg"
                  />

                  <img
                    className="w-2 h-2 left-2 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-84.svg"
                  />
                </div>

                <div className="absolute w-[26px] h-[26px] top-0 left-[7px] rounded-[13px]">
                  <div className="w-[26px] h-[26px] rounded-[13px] border-[0.65px] border-solid absolute top-0 left-0 border-white rotate-180" />

                  <img
                    className="absolute w-[13px] h-px top-[18px] left-1.5"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-125.svg"
                  />

                  <img
                    className="absolute w-[3px] h-1.5 top-3 left-2"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-52.svg"
                  />

                  <img
                    className="absolute w-[3px] h-3 top-[7px] left-3.5"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-53.svg"
                  />

                  <img
                    className="absolute w-[3px] h-[9px] top-[9px] left-[11px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-128.svg"
                  />
                </div>
              </div>

              <div className="absolute w-[74px] h-[92px] top-[235px] left-[488px]">
                <div className="w-[72px] top-[59px] left-0 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[16.5px] text-center leading-[14.7px] absolute tracking-[0]">
                  PLAY
                </div>

                <div className="absolute w-[43px] h-[43px] top-0 left-[15px] rounded-[21.41px]">
                  <div className="absolute w-[43px] h-[43px] top-0 left-0 rounded-[21.41px] border-[1.07px] border-solid border-white rotate-180" />

                  <img
                    className="absolute w-[17px] h-5 top-[11px] left-[15px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-129.svg"
                  />
                </div>

                <div className="absolute w-[13px] h-[13px] top-[79px] left-[3px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-25.svg)] bg-[100%_100%]">
                  <img
                    className="w-[13px] h-[13px] left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-25.svg"
                  />
                </div>

                <div className="absolute w-[13px] h-[13px] top-[79px] left-4 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-86.svg)] bg-[100%_100%]">
                  <img
                    className="w-[13px] h-[13px] left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-86.svg"
                  />
                </div>

                <div className="absolute w-[13px] h-[13px] top-[79px] left-[29px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-87.svg)] bg-[100%_100%]">
                  <img
                    className="w-[13px] h-[13px] left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-87.svg"
                  />
                </div>

                <div className="absolute w-[13px] h-[13px] top-[79px] left-[43px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-28.svg)] bg-[100%_100%]">
                  <img
                    className="w-[13px] h-[13px] left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-28.svg"
                  />
                </div>

                <div className="absolute w-[13px] h-[13px] top-[79px] left-14 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-89.svg)] bg-[100%_100%]">
                  <img
                    className="w-[13px] h-[13px] left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-89.svg"
                  />
                </div>
              </div>

              <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
                <div className="relative w-[549px] h-[23px] overflow-hidden">
                  <div className="inline-flex items-center gap-[9.09px] relative">
                    <div 
                      ref={headerTickerRef}
                      id="header-ticker-area"
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          {dexheroTicker}
                        </div>

                        <div className="top-3 left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          {mockGain}
                        </div>

                        {modelUrl ? (
                          <div className="absolute w-[59px] h-[70px] top-0 left-14 rounded overflow-hidden">
                            <model-viewer
                              src={modelUrl}
                              alt="DexHero Model"
                              style={{
                                width: '100%',
                                height: '100%',
                                backgroundColor: 'transparent'
                              }}
                              camera-orbit="0deg 75deg 105%"
                              disable-zoom
                              disable-pan
                              auto-rotate
                              auto-rotate-delay="0"
                              rotation-per-second="30deg"
                            />
                          </div>
                        ) : (
                          <img
                            className="absolute w-[59px] h-[70px] top-0 left-14"
                            alt="Vector"
                            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                          />
                        )}
                      </div>
                    </div>

                    <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden">
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="relative w-[100px] h-[21px]">
                  <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                    <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                      0x5kb...vl86d
                    </div>
                  </div>
                </div>

                <img
                  className="relative w-[29.7px] h-[29.7px]"
                  alt="Profile"
                  src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                />
              </header>

              <div className="absolute top-[55px] left-[274px] [font-family:'Inter',Helvetica] font-medium text-[#cfd4dc] text-[14.7px] tracking-[0] leading-[22.0px] whitespace-nowrap">
                Pricing
              </div>

              <div className="absolute top-[55px] left-[165px] [font-family:'Inter',Helvetica] font-medium text-gray-50 text-[14.7px] tracking-[0] leading-[22.0px] whitespace-nowrap">
                Collection
              </div>
            </div>

            <img
              className="absolute w-[752px] h-[1858px] top-0 left-0"
              alt="Subtract"
              src="https://c.animaapp.com/Hxjzzvpo/img/subtract.svg"
            />
          </div>
        </div>
      </div>


      {/* Description Popup Modal */}
      {showDescriptionPopup && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShowDescriptionPopup(false)}
        >
          <div 
            className="bg-gray-900 border border-gray-600 rounded-lg p-6 max-w-md w-full mx-4 max-h-96 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-white text-lg font-semibold">DexHero Description</h3>
              <button
                onClick={() => setShowDescriptionPopup(false)}
                className="text-gray-400 hover:text-white text-xl font-bold"
              >
                ×
              </button>
            </div>
            <p className="text-[#cfd4dc] text-sm leading-relaxed">
              {dexheroDescription}
            </p>
          </div>
        </div>
      )}

      {/* Minting Popup Modal */}
      {showMintingPopup && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShowMintingPopup(false)}
        >
          <div 
            className="bg-gray-900 border border-gray-600 rounded-lg p-6 max-w-lg w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-white text-xl font-semibold">Mint Your DexHero</h3>
              <button
                onClick={() => setShowMintingPopup(false)}
                className="text-gray-400 hover:text-white text-xl font-bold"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-6">
              {/* DexHero Summary */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="text-white font-semibold mb-2">DexHero Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Name:</span>
                    <span className="text-white ml-2">{dexheroName}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Ticker:</span>
                    <span className="text-white ml-2">{dexheroTicker}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-400">Token Supply:</span>
                    <span className="text-white ml-2">1,000,000,000 {dexheroTicker}</span>
                  </div>
                </div>
              </div>

              {/* Token Purchase Option */}
              <div>
                <label className="block text-white font-semibold mb-2">
                  Purchase Token Supply (Optional)
                </label>
                <p className="text-gray-400 text-sm mb-3">
                  Would you like to purchase a portion of the {dexheroTicker} token supply? Enter the amount in tokens.
                </p>
                <input
                  type="number"
                  value={tokenPurchaseAmount}
                  onChange={(e) => setTokenPurchaseAmount(e.target.value)}
                  placeholder="Enter amount (e.g., 100000)"
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
                {tokenPurchaseAmount && (
                  <p className="text-blue-400 text-sm mt-1">
                    You will purchase {parseInt(tokenPurchaseAmount).toLocaleString()} {dexheroTicker} tokens
                  </p>
                )}
              </div>

              {/* Wallet Address Confirmation */}
              <div>
                <label className="block text-white font-semibold mb-2">
                  Confirm Wallet Address
                </label>
                <p className="text-gray-400 text-sm mb-3">
                  Please confirm the wallet address where your soulbound DexHero NFT will be sent.
                </p>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="Enter your wallet address (0x...)"
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
              </div>

              {/* Minting Summary */}
              <div className="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-4">
                <h4 className="text-blue-400 font-semibold mb-2">What You'll Receive:</h4>
                <ul className="text-sm text-gray-300 space-y-1">
                  <li>• 1 Soulbound {dexheroName} NFT</li>
                  <li>• Access to play your DexHero in supported games</li>
                  <li>• {dexheroTicker} token with 1B fixed supply</li>
                  {tokenPurchaseAmount && (
                    <li>• {parseInt(tokenPurchaseAmount).toLocaleString()} {dexheroTicker} tokens</li>
                  )}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  onClick={() => setShowMintingPopup(false)}
                  className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Here you would integrate with the blockchain minting process
                    console.log('Minting DexHero:', {
                      name: dexheroName,
                      ticker: dexheroTicker,
                      tokenPurchase: tokenPurchaseAmount,
                      walletAddress: walletAddress,
                      modelUrl: modelUrl
                    });
                    alert('Minting functionality will be implemented with blockchain integration!');
                    setShowMintingPopup(false);
                  }}
                  disabled={!walletAddress}
                  className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-semibold"
                >
                  Mint DexHero
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};
