import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDexHero } from "../../contexts/DexHeroContext";
import '@google/model-viewer';

export const Createanimations = () => {
  const {
    creationData,
    setAnimations,
    validateAnimations,
    updateCreationProgress,
    updateAnimationCustomizations
  } = useDexHero();

  const [selectedAnimations, setSelectedAnimations] = useState(
    creationData.selectedAnimations || []
  );
  const [detectedAnimations, setDetectedAnimations] = useState([]);
  const [availableAnimations, setAvailableAnimations] = useState([]); // Animations not in slots
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnimation, setCurrentAnimation] = useState(null);
  const [modelViewerRef, setModelViewerRef] = useState(null);
  const [activeTooltip, setActiveTooltip] = useState(null);
  const [editingData, setEditingData] = useState({});
  const [draggedAnimation, setDraggedAnimation] = useState(null);
  const [showRemoveConfirmation, setShowRemoveConfirmation] = useState(false);
  const [showKeybindingTutorial, setShowKeybindingTutorial] = useState(false);
  const [showAnimationTutorial, setShowAnimationTutorial] = useState(false);
  const [dropZones, setDropZones] = useState({
    actionSlots: Array(6).fill(null), // 6 action bar slots
    movementControls: {
      up: null,
      down: null,
      left: null,
      right: null,
      jump: null
    }
  });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [hoveredSlot, setHoveredSlot] = useState(null);
  
  // 3-Phase Animation Setup State
  const [currentPhase, setCurrentPhase] = useState(1); // 1: Naming, 2: Controls, 3: Review
  const [currentAnimationIndex, setCurrentAnimationIndex] = useState(0);

  // Use animation customizations from global context
  const animationCustomizations = creationData.animationCustomizations || {};
  
  // Current animation being customized
  const currentAnimationData = detectedAnimations[currentAnimationIndex];
  
  // Get current customization data for the animation
  const getCurrentCustomization = () => {
    if (!currentAnimationData) return { name: '', category: 'Other', iconFile: null, iconPreview: null };

    // Check if we have saved customizations for this animation
    const savedCustomization = animationCustomizations[currentAnimationData.id];

    if (savedCustomization) {
      return {
        name: savedCustomization.name || currentAnimationData.name.replace('Character_', ''),
        category: savedCustomization.category || currentAnimationData.category,
        iconFile: null, // File object can't be persisted, but we have the data URL
        iconPreview: savedCustomization.iconDataUrl || null
      };
    }

    // Return default with detected name pre-filled
    return {
      name: currentAnimationData.name.replace('Character_', ''),
      category: currentAnimationData.category,
      iconFile: null,
      iconPreview: null
    };
  };
  
  const [currentCustomization, setCurrentCustomization] = useState(getCurrentCustomization());
  
  // Pokemon-style card state
  const [showReviewCard, setShowReviewCard] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [tokenSupplyToBuy, setTokenSupplyToBuy] = useState(100000);

  // Animation categorization keywords
  const categoryKeywords = {
    'Movement': ['walk', 'run', 'sprint', 'jog', 'move', 'step', 'stride', 'pace'],
    'Combat': ['attack', 'fight', 'punch', 'kick', 'slash', 'stab', 'defend', 'block', 'guard', 'parry', 'dodge', 'death', 'die', 'defeat'],
    'Idle': ['idle', 'stand', 'rest', 'wait', 'neutral', 'default', 'base'],
    'Emotes': ['wave', 'dance', 'celebrate', 'victory', 'cheer', 'bow', 'salute', 'point', 'thumbs', 'clap', 'laugh', 'cry', 'angry', 'sad', 'happy'],
    'Actions': ['jump', 'climb', 'crawl', 'crouch', 'sit', 'lie', 'fall', 'roll', 'slide', 'grab', 'pick', 'throw', 'push', 'pull'],
    'Magic': ['cast', 'spell', 'magic', 'summon', 'enchant', 'heal', 'buff', 'debuff', 'teleport'],
    'Tools': ['swing', 'hammer', 'axe', 'pickaxe', 'shovel', 'build', 'craft', 'mine', 'chop', 'cut']
  };

  // Function to categorize animation based on name
  const categorizeAnimation = (animationName) => {
    const lowerName = animationName.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => lowerName.includes(keyword))) {
        return category;
      }
    }
    
    return 'Other';
  };

  // Function to get icon for category
  const getCategoryIcon = (category) => {
    const icons = {
      'Movement': '🚶',
      'Combat': '⚔️',
      'Idle': '🧍',
      'Emotes': '😊',
      'Actions': '🤸',
      'Magic': '✨',
      'Tools': '🔨',
      'Other': (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 15 15" fill="none">
          <path d="M2 14H13C13.5523 14 14 13.5523 14 13V2C14 1.44771 13.5523 1 13 1H2C1.44771 1 1 1.44771 1 2V13C1 13.5523 1.44771 14 2 14Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M2.25 13.9998L9.55 7.11972C9.63984 7.04489 9.75307 7.00391 9.87 7.00391C9.98692 7.00391 10.1002 7.04489 10.19 7.11972L14 9.84972" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M5 6.5C5.82843 6.5 6.5 5.82843 6.5 5C6.5 4.17157 5.82843 3.5 5 3.5C4.17157 3.5 3.5 4.17157 3.5 5C3.5 5.82843 4.17157 6.5 5 6.5Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    };
    return icons[category] || icons['Other'];
  };

  // Analyze GLB file for animations
  const analyzeModelAnimations = async () => {
    if (!creationData.modelFile) return;

    setIsAnalyzing(true);
    
    try {
      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Basic GLB animation detection
      // In a real implementation, this would use Three.js GLTFLoader to parse the actual GLB file
      // For now, we'll implement a basic detection system that works with uploaded GLB files
      
      if (creationData.modelUrl) {
        // Try to detect animations from the GLB file
        // This is a simplified approach - in production you'd use Three.js GLTFLoader
        const detectedAnims = await detectAnimationsFromGLB(creationData.modelUrl);
        setDetectedAnimations(detectedAnims);
      } else {
        setDetectedAnimations([]);
      }
      
    } catch (error) {
      console.error('Error analyzing model animations:', error);
      setDetectedAnimations([]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Real GLB animation detection function using Three.js
  const detectAnimationsFromGLB = async (modelUrl) => {
    try {
      // Import Three.js modules dynamically
      const THREE = await import('three');
      const { GLTFLoader } = await import('three/examples/jsm/loaders/GLTFLoader.js');
      
      const loader = new GLTFLoader();
      
      return new Promise((resolve, reject) => {
        loader.load(
          modelUrl,
          (gltf) => {
            console.log('GLB loaded successfully:', gltf);
            
            // Extract animations from the GLTF
            const animations = gltf.animations || [];
            console.log('Found animations:', animations);
            
            if (animations.length === 0) {
              console.log('No animations found in GLB file');
              resolve([]);
              return;
            }
            
            // Process each animation
            const detectedAnimations = animations.map((animation, index) => {
              const animationName = animation.name || `Animation_${index + 1}`;
              const category = categorizeAnimation(animationName);
              const duration = animation.duration || 1.0;
              
              console.log(`Processing animation: ${animationName}, duration: ${duration}, category: ${category}`);
              
              return {
                id: `detected_${index}`,
                name: animationName,
                originalName: animationName,
                category: category,
                duration: duration,
                detected: true,
                animationClip: animation // Store the actual Three.js AnimationClip
              };
            });
            
            console.log('Processed animations:', detectedAnimations);
            resolve(detectedAnimations);
          },
          (progress) => {
            console.log('Loading progress:', progress);
          },
          (error) => {
            console.error('Error loading GLB file:', error);
            reject(error);
          }
        );
      });
      
    } catch (error) {
      console.error('Error importing Three.js or loading GLB:', error);
      return [];
    }
  };

  // Clear all animation state when model is replaced
  useEffect(() => {
    // Reset all local animation state when model URL changes (indicating replacement)
    setDetectedAnimations([]);
    setAvailableAnimations([]);
    setSelectedAnimations([]);
    setCurrentPhase(1);
    setCurrentAnimationIndex(0);
    setCurrentCustomization({ name: '', category: 'Other', iconFile: null, iconPreview: null });
    setEditingData({});
    setDropZones({
      actionSlots: Array(6).fill(null),
      movementControls: {
        up: null,
        down: null,
        left: null,
        right: null,
        jump: null
      }
    });
    setShowKeybindingTutorial(false);
    setCurrentAnimation(null);
    setDraggedAnimation(null);
    setShowRemoveConfirmation(false);
  }, [creationData.modelUrl]);

  // Analyze animations when model is uploaded
  useEffect(() => {
    if (creationData.modelFile && detectedAnimations.length === 0) {
      analyzeModelAnimations();
    }
  }, [creationData.modelFile]);

  // Handle animation selection
  const toggleAnimation = (animationId) => {
    const newSelected = selectedAnimations.includes(animationId)
      ? selectedAnimations.filter(id => id !== animationId)
      : [...selectedAnimations, animationId];
    
    setSelectedAnimations(newSelected);
    
    // Update context with custom icon data
    const selectedAnimationObjects = detectedAnimations.filter(anim => 
      newSelected.includes(anim.id)
    ).map(anim => {
      const editData = editingData[anim.id];
      return {
        ...anim,
        customName: editData?.name || anim.name,
        customCategory: editData?.category || anim.category,
        hasCustomIcon: editData?.hasCustomIcon || false,
        iconDataUrl: editData?.iconDataUrl || null
      };
    });
    setAnimations(selectedAnimationObjects, newSelected);
    validateAnimations();
  };

  // Handle animation playback
  const playAnimation = (animation) => {
    // If clicking the same animation that's currently playing, stop it
    if (currentAnimation?.id === animation.id) {
      setCurrentAnimation(null);
      stopCurrentAnimation();
      console.log(`Stopping animation: ${animation.name}`);
    } else {
      // Play the new animation
      setCurrentAnimation(animation);
      playAnimationOnModel(animation);
      console.log(`Playing animation: ${animation.name}`);
    }
  };

  // Function to play animation on the model-viewer
  const playAnimationOnModel = (animation) => {
    const modelViewer = document.querySelector('model-viewer');
    if (modelViewer) {
      try {
        // Use model-viewer's animation API
        const animationName = animation.name;
        console.log(`Attempting to play animation: ${animationName}`);
        
        // Set the animation name on the model-viewer
        modelViewer.animationName = animationName;
        
        // Play the animation
        modelViewer.play();
        
        console.log(`Successfully started animation: ${animationName}`);
      } catch (error) {
        console.error('Error playing animation on model:', error);
      }
    } else {
      console.error('Model viewer not found');
    }
  };

  // Function to stop current animation
  const stopCurrentAnimation = () => {
    const modelViewer = document.querySelector('model-viewer');
    if (modelViewer) {
      try {
        // Pause the animation
        modelViewer.pause();
        console.log('Animation stopped');
      } catch (error) {
        console.error('Error stopping animation:', error);
      }
    }
  };

  // Effect to handle model viewer reference and setup
  useEffect(() => {
    const modelViewer = document.querySelector('model-viewer');
    if (modelViewer) {
      setModelViewerRef(modelViewer);
      
      // Add event listeners for animation events
      const handleAnimationFinished = () => {
        console.log('Animation finished');
        setCurrentAnimation(null);
      };
      
      modelViewer.addEventListener('finished', handleAnimationFinished);
      
      // Cleanup
      return () => {
        modelViewer.removeEventListener('finished', handleAnimationFinished);
      };
    }
  }, [detectedAnimations]);

  // Auto-play current animation when it changes
  useEffect(() => {
    if (currentAnimationData && currentPhase === 1) {
      playAnimation(currentAnimationData);
    }
  }, [currentAnimationIndex, currentPhase]);

  // Auto-play first animation when animations are detected
  useEffect(() => {
    if (detectedAnimations.length > 0 && currentPhase === 1 && currentAnimationIndex === 0) {
      // Small delay to ensure model-viewer is ready
      const timer = setTimeout(() => {
        if (currentAnimationData) {
          playAnimation(currentAnimationData);
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [detectedAnimations.length, currentPhase]);

  // Update current customization when animation changes or customizations are loaded
  useEffect(() => {
    if (currentAnimationData) {
      setCurrentCustomization(getCurrentCustomization());
    }
  }, [currentAnimationIndex, animationCustomizations, currentAnimationData]);

  // Initialize available animations when detected animations change
  useEffect(() => {
    if (detectedAnimations.length > 0) {
      updateAvailableAnimations(dropZones);
    }
  }, [detectedAnimations, dropZones]);

  // Show animation customization tutorial when animations are first detected
  useEffect(() => {
    const hasSeenAnimationTutorial = localStorage.getItem('hasSeenAnimationTutorial');
    if (detectedAnimations.length > 0 && currentPhase === 1 && !hasSeenAnimationTutorial) {
      const timer = setTimeout(() => {
        setShowAnimationTutorial(true);
      }, 1000); // Longer delay to let animations load

      return () => clearTimeout(timer);
    }
  }, [detectedAnimations.length, currentPhase]);

  // Show keybinding tutorial when entering Phase 2
  useEffect(() => {
    if (currentPhase === 2 && detectedAnimations.length > 0) {
      // Small delay to ensure the phase transition is complete
      const timer = setTimeout(() => {
        setShowKeybindingTutorial(true);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [currentPhase, detectedAnimations.length]);

  // Phase 1 Functions - Animation Customization
  const handleIconUpload = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setCurrentCustomization(prev => ({
          ...prev,
          iconFile: file,
          iconPreview: event.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleKeywordChange = (index, value) => {
    // Remove spaces from the value
    const cleanValue = value.replace(/\s/g, '');
    const newKeywords = [...currentCustomization.keywords];
    newKeywords[index] = cleanValue;
    setCurrentCustomization(prev => ({
      ...prev,
      keywords: newKeywords
    }));
  };

  const handleKeywordKeyPress = (e, index) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const currentValue = currentCustomization.keywords[index].trim();
      
      // Only add new keyword if current one has content and we're under the limit
      if (currentValue && currentCustomization.keywords.length < 3) {
        setCurrentCustomization(prev => ({
          ...prev,
          keywords: [...prev.keywords, '']
        }));
      }
    }
  };

  const removeKeyword = (index) => {
    if (currentCustomization.keywords.length > 1) {
      const newKeywords = currentCustomization.keywords.filter((_, i) => i !== index);
      setCurrentCustomization(prev => ({
        ...prev,
        keywords: newKeywords
      }));
    }
  };

  const saveCurrentAnimation = () => {
    if (!currentAnimationData) return;

    // Save current customization to global context
    const newCustomizations = {
      [currentAnimationData.id]: {
        name: currentCustomization.name,
        category: currentCustomization.category,
        hasCustomIcon: !!currentCustomization.iconFile,
        iconDataUrl: currentCustomization.iconPreview
      }
    };
    updateAnimationCustomizations(newCustomizations);

    // Move to next animation or next phase
    if (currentAnimationIndex < detectedAnimations.length - 1) {
      setCurrentAnimationIndex(currentAnimationIndex + 1);
    } else {
      // All animations customized, move to phase 2
      setCurrentPhase(2);
    }
  };

  const goToPreviousAnimation = () => {
    if (currentAnimationIndex > 0) {
      setCurrentAnimationIndex(currentAnimationIndex - 1);
    }
  };

  const removeCurrentAnimation = () => {
    if (!currentAnimationData) return;

    // Remove animation from detected animations
    const newDetectedAnimations = detectedAnimations.filter(anim => anim.id !== currentAnimationData.id);
    setDetectedAnimations(newDetectedAnimations);

    // Remove from customizations
    const newCustomizations = { ...animationCustomizations };
    delete newCustomizations[currentAnimationData.id];
    updateAnimationCustomizations(newCustomizations);

    // Adjust current index if needed
    if (currentAnimationIndex >= newDetectedAnimations.length && newDetectedAnimations.length > 0) {
      setCurrentAnimationIndex(newDetectedAnimations.length - 1);
    } else if (newDetectedAnimations.length === 0) {
      setCurrentPhase(2); // Move to phase 2 if no animations left
    }

    setShowRemoveConfirmation(false);
  };

  const handleAnimationTutorialClose = () => {
    setShowAnimationTutorial(false);
    localStorage.setItem('hasSeenAnimationTutorial', 'true');
  };

  // Group detected animations by category
  const groupedAnimations = detectedAnimations.reduce((groups, animation) => {
    const category = animation.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(animation);
    return groups;
  }, {});

  // Function to get all animations currently in slots
  const getAnimationsInSlots = (dropZones) => {
    const animationsInSlots = [];

    // Get animations from action slots
    dropZones.actionSlots.forEach(animation => {
      if (animation) animationsInSlots.push(animation.id);
    });

    // Get animations from movement controls
    Object.values(dropZones.movementControls).forEach(animation => {
      if (animation) animationsInSlots.push(animation.id);
    });

    return animationsInSlots;
  };

  // Function to update available animations based on what's in slots
  const updateAvailableAnimations = (newDropZones) => {
    const animationsInSlots = getAnimationsInSlots(newDropZones);
    const available = detectedAnimations.filter(animation =>
      !animationsInSlots.includes(animation.id)
    );
    setAvailableAnimations(available);
  };

  // Function to remove animation from slot
  const removeAnimationFromSlot = (slotType, slotIndex = null) => {
    const newDropZones = { ...dropZones };

    if (slotType === 'action') {
      newDropZones.actionSlots[slotIndex] = null;
    } else {
      newDropZones.movementControls[slotType] = null;
    }

    setDropZones(newDropZones);
    updateAvailableAnimations(newDropZones);
  };

  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full"
      data-model-id="1305:16763"
    >
      <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
          <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
            <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
              <div className="inline-flex items-center gap-[9.09px] relative">
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
              </div>
            </div>

            <div className="relative w-[100px] h-[21px]">
              <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                  0x5kb...vl86d
                </div>
              </div>
            </div>

            <img
              className="relative w-[29.7px] h-[29.7px]"
              alt="Profile"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </header>

          <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
            {/* Back Button */}
            <Link
              to="/create"
              className="absolute w-[57px] h-[21px] top-[7px] left-[547px] cursor-pointer z-10"
            >
              <div className="absolute w-[41px] top-0 left-[15px] [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] tracking-[0] leading-[20.5px] hover:text-white transition-colors">
                Back
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
              />
            </Link>

            {/* Next Button */}
            <Link
              to="/reviewdexhero"
              className="absolute w-[57px] h-[21px] top-[7px] left-[618px] cursor-pointer z-10"
            >
              <div className="w-[41px] top-0 left-0 [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] leading-[20.5px] absolute tracking-[0] hover:text-white transition-colors">
                Next
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] right-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                style={{ transform: 'scaleX(-1)' }}
              />
            </Link>

            <div className="absolute w-[675px] h-[440px] top-0 left-0">
              <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

              {/* New Layout - Using Full Frame Space */}
              <div className="absolute w-[657px] h-[378px] top-[42px] left-[9px]">
                {!creationData.modelFile ? (
                  <div className="w-full h-full flex items-center justify-center border-2 border-dashed border-[#3a3a3a] rounded-lg">
                    <div className="text-center">
                      <p className="[font-family:'Lato',Helvetica] font-medium text-[#8d8d8d] text-lg mb-2">
                        No 3D Model Uploaded
                      </p>
                      <p className="[font-family:'Lato',Helvetica] font-light text-[#5a5a5a] text-sm">
                        Please upload a 3D model first to detect animations
                      </p>
                    </div>
                  </div>
                ) : isAnalyzing ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-8 h-8 border-2 border-[#08f7fe] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="[font-family:'Lato',Helvetica] font-medium text-white text-lg mb-2">
                        Analyzing Model...
                      </p>
                      <p className="[font-family:'Lato',Helvetica] font-light text-[#8d8d8d] text-sm">
                        Detecting animations in your GLB file
                      </p>
                    </div>
                  </div>
                ) : currentPhase === 1 && detectedAnimations.length > 0 ? (
                  /* Phase 1: Animation Customization */
                  <div className="w-full h-full flex">
                    {/* Left Panel - Customization Form */}
                    <div className="w-[280px] h-full bg-transparent border border-[#3a3a3a] rounded-l-lg p-3 flex flex-col">
                      {/* Progress */}
                      <div className="mb-6">
                        <div className="flex justify-between items-center mb-1">
                          <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs">
                            Detected Animation {currentAnimationIndex + 1} of {detectedAnimations.length}
                          </span>
                          <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs">
                            {Math.round(((currentAnimationIndex) / detectedAnimations.length) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-[#2a2a2a] rounded-full h-1.5">
                          <div 
                            className="bg-gradient-to-r from-[#08f7fe] to-[#0074e0] h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${(currentAnimationIndex / detectedAnimations.length) * 100}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Current Animation Info */}
                      {currentAnimationData && (
                        <div className="mb-6 p-3 bg-gray-600/40 rounded-lg border border-[#3a3a3a]">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 flex items-center justify-center flex-shrink-0">
                              {currentCustomization.iconPreview ? (
                                <img 
                                  src={currentCustomization.iconPreview} 
                                  alt="Custom icon" 
                                  className="w-10 h-10 rounded object-cover"
                                />
                              ) : (
                                <span className="text-lg">{getCategoryIcon(currentCustomization.category)}</span>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="[font-family:'Lexend',Helvetica] text-white text-sm font-medium truncate">
                                {currentCustomization.name || currentAnimationData.name.replace('Character_', '')}
                              </h4>
                              <p className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs">
                                {currentCustomization.category} • {currentAnimationData.duration.toFixed(1)}s
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Animation Name */}
                      <div className="mb-6">
                        <label className="block [font-family:'Lato',Helvetica] text-white text-sm font-medium mb-1">
                          Animation Name
                        </label>
                        <input
                          type="text"
                          value={currentCustomization.name}
                          onChange={(e) => setCurrentCustomization(prev => ({ ...prev, name: e.target.value }))}
                          placeholder={currentAnimationData?.name.replace('Character_', '') || "Enter animation name"}
                          className="w-full px-2 py-1.5 bg-gray-600/40 border border-[#4a4a4a] rounded text-white text-sm focus:border-[#08f7fe] focus:outline-none transition-colors"
                          style={{ fontFamily: 'Lato, Helvetica' }}
                        />
                      </div>

                      {/* Icon & Category Selection */}
                      <div className="mb-6 flex-1">
                        <label className="block [font-family:'Lato',Helvetica] text-white text-sm font-medium mb-1">
                          Icon & Category
                        </label>
                        <div className="flex items-center space-x-2">
                          <div className="w-12 h-12 rounded border-2 border-dashed border-[#4a4a4a] bg-transparent flex items-center justify-center flex-shrink-0 cursor-pointer hover:border-[#6a6a6a] transition-colors">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleIconUpload}
                              className="hidden"
                              id="icon-upload"
                            />
                            <label htmlFor="icon-upload" className="cursor-pointer w-full h-full flex items-center justify-center">
                              {currentCustomization.iconPreview ? (
                                <img 
                                  src={currentCustomization.iconPreview} 
                                  alt="Icon preview" 
                                  className="w-10 h-10 rounded object-cover"
                                />
                              ) : (
                                <span className="text-lg">{getCategoryIcon(currentCustomization.category)}</span>
                              )}
                            </label>
                          </div>
                          <div className="flex-1">
                            <select
                              value={currentCustomization.category}
                              onChange={(e) => setCurrentCustomization(prev => ({ ...prev, category: e.target.value }))}
                              className="w-full px-2 py-1.5 bg-gray-600/40 border border-[#4a4a4a] rounded text-white text-sm focus:border-[#08f7fe] focus:outline-none transition-colors"
                              style={{ fontFamily: 'Lato, Helvetica' }}
                            >
                              <option value="Movement">🚶 Movement</option>
                              <option value="Combat">⚔️ Combat</option>
                              <option value="Idle">🧍 Idle</option>
                              <option value="Emotes">😊 Emotes</option>
                              <option value="Actions">🤸 Actions</option>
                              <option value="Magic">✨ Magic</option>
                              <option value="Tools">🔨 Tools</option>
                              <option value="Other">📷 Other</option>
                            </select>
                          </div>
                        </div>
                      </div>

                      {/* Navigation Buttons */}
                      <div className="flex space-x-2 mt-auto">
                        <button
                          onClick={goToPreviousAnimation}
                          disabled={currentAnimationIndex === 0}
                          className="flex-1 px-3 py-1.5 bg-[#3a3a3a] hover:bg-[#4a4a4a] disabled:bg-[#2a2a2a] disabled:cursor-not-allowed border border-[#5a5a5a] rounded text-white text-xs font-medium transition-colors"
                        >
                          Previous
                        </button>
                        <button
                          onClick={saveCurrentAnimation}
                          disabled={!currentCustomization.name.trim()}
                          className="flex-1 px-3 py-1.5 bg-[#3a3a3a] hover:bg-[#4a4a4a] disabled:bg-[#2a2a2a] disabled:cursor-not-allowed border border-[#5a5a5a] rounded text-white text-xs font-medium transition-colors"
                        >
                          {currentAnimationIndex < detectedAnimations.length - 1 ? 'Next' : 'Finish'}
                        </button>
                      </div>

                      {/* Remove Animation Text */}
                      <div className="mt-3.3 text-center">
                        <span
                          onClick={() => setShowRemoveConfirmation(true)}
                          className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs hover:text-red-400 transition-colors cursor-pointer"
                        >
                          Remove Animation
                        </span>
                      </div>
                    </div>

                    {/* Right Panel - 3D Model Viewer */}
                    <div className="flex-1 h-full bg-transparent border-t border-r border-b border-[#3a3a3a] rounded-r-lg relative">
                      <model-viewer
                        src={creationData.modelUrl}
                        alt="3D Model with Animations"
                        auto-rotate
                        camera-controls
                        style={{
                          width: '100%',
                          height: '100%',
                          backgroundColor: 'transparent'
                        }}
                        environment-image="neutral"
                        shadow-intensity="1"
                        camera-orbit="0deg 75deg 105%"
                      />
                      
                      {/* Animation Playing Indicator */}
                      {currentAnimation && (
                        <div className="absolute top-2 right-2 bg-[#1a1a1a] bg-opacity-90 rounded px-2 py-1 border border-[#08f7fe]">
                          <div className="flex items-center space-x-1">
                            <div className="w-1.5 h-1.5 bg-[#08f7fe] rounded-full animate-pulse"></div>
                            <span className="[font-family:'Lato',Helvetica] text-white text-xs">
                              {currentCustomization.name || currentAnimation.name.replace('Character_', '')}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Remove Animation Confirmation Popup */}
                    {showRemoveConfirmation && (
                      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                        <div className="bg-[#2a2a2a] border border-[#4a4a4a] rounded-lg p-6 max-w-md mx-4">
                          <h3 className="text-white text-lg font-medium mb-4">Remove Animation</h3>
                          <p className="text-[#d8d8e7] text-sm mb-4">
                            Are you sure you want to remove "{currentAnimationData?.name.replace('Character_', '')}" from the animation list?
                          </p>
                          <p className="text-[#8d8d8d] text-xs mb-6">
                            This animation can be added back by re-uploading the 3D model.
                          </p>
                          <div className="flex space-x-3">
                            <button
                              onClick={() => setShowRemoveConfirmation(false)}
                              className="flex-1 px-4 py-2 bg-[#3a3a3a] hover:bg-[#4a4a4a] border border-[#5a5a5a] rounded text-white text-sm transition-colors"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={removeCurrentAnimation}
                              className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 border border-red-500 rounded text-white text-sm transition-colors"
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : currentPhase === 2 && detectedAnimations.length > 0 ? (
                  /* Phase 2: Keyboard Controls Mapping - New Layout */
                  <div className="w-full h-full flex flex-col">
                    {/* Top Section - Animation List and Model Viewer */}
                    <div className="h-[200px] flex flex-shrink-0">
                      {/* Left Panel - Animation List */}
                      <div className="w-[280px] h-full bg-transparent border border-[#3a3a3a] rounded-l-lg p-3 flex flex-col flex-shrink-0">

                        {/* Animation List */}
                        <div className="h-[170px] overflow-y-auto">
                          <div className="space-y-2 min-h-full">
                            {availableAnimations.map((animation) => {
                              const customization = animationCustomizations[animation.id];
                              const displayName = customization?.name || animation.name.replace('Character_', '');
                              const category = customization?.category || animation.category;
                              const iconPreview = customization?.iconDataUrl;

                              return (
                                <div
                                  key={animation.id}
                                  draggable
                                  onDragStart={(e) => {
                                    setDraggedAnimation(animation);
                                    e.dataTransfer.effectAllowed = 'move';
                                  }}
                                  onClick={() => playAnimation(animation)}
                                  className="flex items-center space-x-2 p-2 bg-gray-600/40 rounded border border-[#4a4a4a] cursor-pointer hover:bg-gray-600/60 transition-colors"
                                >
                                  <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                                    {iconPreview ? (
                                      <img 
                                        src={iconPreview} 
                                        alt="Custom icon" 
                                        className="w-6 h-6 rounded object-cover"
                                      />
                                    ) : (
                                      <span className="text-sm">{getCategoryIcon(category)}</span>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="[font-family:'Lato',Helvetica] text-white text-sm font-medium truncate">
                                      {displayName}
                                    </p>
                                    <p className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs">
                                      {category}
                                    </p>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>

                      </div>

                      {/* Right Panel - 3D Model Viewer */}
                      <div className="flex-1 h-full bg-transparent border-t border-r border-b border-[#3a3a3a] rounded-r-lg relative">
                        <model-viewer
                          src={creationData.modelUrl}
                          alt="3D Model with Animations"
                          auto-rotate
                          camera-controls
                          style={{
                            width: '100%',
                            height: '100%',
                            backgroundColor: 'transparent'
                          }}
                          environment-image="neutral"
                          shadow-intensity="1"
                          camera-orbit="0deg 75deg 105%"
                        />
                        
                        {/* Animation Playing Indicator */}
                        {currentAnimation && (
                          <div className="absolute top-2 right-2 bg-[#1a1a1a] bg-opacity-90 rounded px-2 py-1 border border-[#08f7fe]">
                            <div className="flex items-center space-x-1">
                              <div className="w-1.5 h-1.5 bg-[#08f7fe] rounded-full animate-pulse"></div>
                              <span className="[font-family:'Lato',Helvetica] text-white text-xs">
                                {currentCustomization.name || currentAnimation.name.replace('Character_', '')}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Bottom Section - Keyboard Layout */}
                    <div className="flex-1 bg-transparent border-t border-[#3a3a3a] p-4 flex justify-center items-center">
                      <div className="w-full max-w-[620px]">
                        {/* Complete Keyboard Layout - All 3 lines with equal spacing */}
                        <div className="space-y-3">
                          {/* First Line - Action Bar (1-0 keys) */}
                          <div>
                            <div className="flex justify-center space-x-2">
                              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map((num) => (
                                <div
                                  key={num}
                                  onDragOver={(e) => e.preventDefault()}
                                  onDrop={(e) => {
                                    e.preventDefault();
                                    if (draggedAnimation) {
                                      const newDropZones = { ...dropZones };
                                      newDropZones.actionSlots[num === 0 ? 9 : num - 1] = draggedAnimation;
                                      setDropZones(newDropZones);
                                      updateAvailableAnimations(newDropZones);
                                      setDraggedAnimation(null);
                                    }
                                  }}
                                  className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors cursor-pointer"
                                  onClick={() => {
                                    const animation = dropZones.actionSlots[num === 0 ? 9 : num - 1];
                                    if (animation) {
                                      playAnimation(animation);
                                    }
                                  }}
                                >
                                  {dropZones.actionSlots[num === 0 ? 9 : num - 1] ? (
                                    <div className="relative w-full h-full">
                                      {animationCustomizations[dropZones.actionSlots[num === 0 ? 9 : num - 1].id]?.iconDataUrl ? (
                                        <img
                                          src={animationCustomizations[dropZones.actionSlots[num === 0 ? 9 : num - 1].id].iconDataUrl}
                                          alt="Animation icon"
                                          className="w-full h-full rounded object-cover"
                                        />
                                      ) : (
                                        <div className="w-full h-full flex items-center justify-center">
                                          <span className="text-xs">{getCategoryIcon(dropZones.actionSlots[num === 0 ? 9 : num - 1].category)}</span>
                                        </div>
                                      )}
                                      <span
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          removeAnimationFromSlot('action', num === 0 ? 9 : num - 1);
                                        }}
                                        className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer bg-black/50 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                                      >
                                        ×
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs font-medium">
                                      {num}
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Second Line - Shift, C on left, Up Arrow positioned above down arrow */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-6">
                              {/* Shift Key */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.sprint = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                onClick={() => {
                                  const animation = dropZones.movementControls.sprint;
                                  if (animation) {
                                    playAnimation(animation);
                                  }
                                }}
                                className="w-20 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors cursor-pointer"
                              >
                                {dropZones.movementControls.sprint ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.sprint.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.sprint.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.sprint.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('sprint');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-xs font-medium">
                                  Sprint
                                </span>
                                )}
                              </div>

                              {/* C Key */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.crouch = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                onClick={() => {
                                  const animation = dropZones.movementControls.crouch;
                                  if (animation) {
                                    playAnimation(animation);
                                  }
                                }}
                                className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors cursor-pointer"
                              >
                                {dropZones.movementControls.crouch ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.crouch.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.crouch.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.crouch.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('crouch');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                  <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm font-medium">
                                    C
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Up Arrow - positioned to align directly above down arrow (middle of arrow cluster) */}
                            <div className="flex space-x-2">
                              {/* Invisible spacer to match left arrow width */}
                              <div className="w-12"></div>
                              
                              {/* Up Arrow - positioned above down arrow */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.up = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                onClick={() => {
                                  const animation = dropZones.movementControls.up;
                                  if (animation) {
                                    playAnimation(animation);
                                  }
                                }}
                                className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors cursor-pointer"
                              >
                                {dropZones.movementControls.up ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.up.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.up.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.up.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('up');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                  <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm">↑</span>
                                )}
                              </div>
                              
                              {/* Invisible spacer to match right arrow width */}
                              <div className="w-12"></div>
                            </div>
                          </div>

                          {/* Third Line - Space, Left, Down, Right */}
                          <div className="flex items-center justify-between">
                            {/* Space Bar - Left side */}
                            <div
                              onDragOver={(e) => e.preventDefault()}
                              onDrop={(e) => {
                                e.preventDefault();
                                if (draggedAnimation) {
                                  const newDropZones = { ...dropZones };
                                  newDropZones.movementControls.jump = draggedAnimation;
                                  setDropZones(newDropZones);
                                  updateAvailableAnimations(newDropZones);
                                  setDraggedAnimation(null);
                                }
                              }}
                              className="w-64 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors"
                            >
                              {dropZones.movementControls.jump ? (
                                <div className="relative w-full h-full flex items-center justify-center">
                                  <div className="w-6 h-6 flex items-center justify-center">
                                    {animationCustomizations[dropZones.movementControls.jump.id]?.iconDataUrl ? (
                                      <img 
                                        src={animationCustomizations[dropZones.movementControls.jump.id].iconDataUrl} 
                                        alt="Animation icon" 
                                        className="w-5 h-5 rounded object-cover"
                                      />
                                    ) : (
                                      <span className="text-xs">{getCategoryIcon(dropZones.movementControls.jump.category)}</span>
                                    )}
                                  </div>
                                  <span
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      removeAnimationFromSlot('jump');
                                    }}
                                    className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                  >
                                    ×
                                  </span>
                                </div>
                              ) : (
                                <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm font-medium">
                                  Jump
                                </span>
                              )}
                            </div>

                            {/* Arrow cluster - Right side, positioned below up arrow */}
                            <div className="flex space-x-2">
                              {/* Left Arrow */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.left = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors"
                              >
                                {dropZones.movementControls.left ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.left.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.left.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.left.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('left');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                  <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm">←</span>
                                )}
                              </div>

                              {/* Down Arrow */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.down = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors"
                              >
                                {dropZones.movementControls.down ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.down.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.down.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.down.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('down');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                  <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm">↓</span>
                                )}
                              </div>

                              {/* Right Arrow */}
                              <div
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                  e.preventDefault();
                                  if (draggedAnimation) {
                                    const newDropZones = { ...dropZones };
                                    newDropZones.movementControls.right = draggedAnimation;
                                    setDropZones(newDropZones);
                                    updateAvailableAnimations(newDropZones);
                                    setDraggedAnimation(null);
                                  }
                                }}
                                className="w-12 h-12 bg-gray-600/40 border-2 border-dashed border-[#4a4a4a] rounded flex items-center justify-center hover:border-[#6a6a6a] transition-colors"
                              >
                                {dropZones.movementControls.right ? (
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <div className="w-6 h-6 flex items-center justify-center">
                                      {animationCustomizations[dropZones.movementControls.right.id]?.iconDataUrl ? (
                                        <img 
                                          src={animationCustomizations[dropZones.movementControls.right.id].iconDataUrl} 
                                          alt="Animation icon" 
                                          className="w-5 h-5 rounded object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs">{getCategoryIcon(dropZones.movementControls.right.category)}</span>
                                      )}
                                    </div>
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeAnimationFromSlot('right');
                                      }}
                                      className="absolute top-0 right-0 text-white text-sm font-bold cursor-pointer"
                                    >
                                      ×
                                    </span>
                                  </div>
                                ) : (
                                  <span className="[font-family:'Lato',Helvetica] text-[#8d8d8d] text-sm">→</span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Phase 2 Completion Button */}
                    <div className="flex justify-center mt-4 pb-4">
                      <Link
                        to="/reviewdexhero"
                        className="px-6 py-2 bg-[#369ad3] hover:bg-[#08f7fe] text-white font-medium rounded transition-colors"
                      >
                        Continue to Review
                      </Link>
                    </div>

                    {/* Keybinding Tutorial Popup */}
                    {showKeybindingTutorial && (
                      <div className="absolute inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
                        <div className="bg-gradient-to-br from-[#1a1a1a] to-[#0d1117] border-2 border-[#08f7fe]/30 rounded-xl p-6 max-w-md mx-4 shadow-2xl">
                          {/* Header with gaming accent */}
                          <div className="flex items-center mb-4">
                            <div className="w-8 h-8 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] rounded-lg flex items-center justify-center mr-3">
                              <span className="text-black font-bold text-lg">⚡</span>
                            </div>
                            <h3 className="[font-family:'Lexend',Helvetica] text-white text-xl font-semibold">
                              Animation Keybinding
                            </h3>
                          </div>

                          {/* Simplified instruction */}
                          <p className="[font-family:'Lato',Helvetica] text-[#e6edf3] text-base mb-6 leading-relaxed text-center">
                            Drag your animations to the correct slots for binding.
                          </p>

                          {/* Action button */}
                          <div className="flex justify-center">
                            <button
                              onClick={() => setShowKeybindingTutorial(false)}
                              className="px-8 py-3 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] hover:from-[#06d4e0] hover:to-[#005bb5] text-black font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                              <span className="[font-family:'Lexend',Helvetica]">GOT IT</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Animation Customization Tutorial Popup */}
                    {showAnimationTutorial && (
                      <div className="absolute inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
                        <div className="bg-gradient-to-br from-[#1a1a1a] to-[#0d1117] border-2 border-[#08f7fe]/30 rounded-xl p-6 max-w-md mx-4 shadow-2xl">
                          {/* Header with gaming accent */}
                          <div className="flex items-center mb-4">
                            <div className="w-8 h-8 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] rounded-lg flex items-center justify-center mr-3">
                              <span className="text-black font-bold text-lg">✨</span>
                            </div>
                            <h3 className="[font-family:'Lexend',Helvetica] text-white text-xl font-semibold">
                              Animation Customization
                            </h3>
                          </div>

                          {/* Instruction */}
                          <p className="[font-family:'Lato',Helvetica] text-[#e6edf3] text-base mb-6 leading-relaxed text-center">
                            Customize each detected animation by giving it a name and selecting a category. You can also upload custom icons.
                          </p>

                          {/* Action button */}
                          <div className="flex justify-center">
                            <button
                              onClick={handleAnimationTutorialClose}
                              className="px-8 py-3 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] hover:from-[#06d4e0] hover:to-[#005bb5] text-black font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                              <span className="[font-family:'Lexend',Helvetica]">GOT IT</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  /* No animations detected or other phases */
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <p className="[font-family:'Lato',Helvetica] font-medium text-[#8d8d8d] text-lg mb-2">
                        No Animations Detected
                      </p>
                      <p className="[font-family:'Lato',Helvetica] font-light text-[#5a5a5a] text-sm">
                        Your GLB file doesn't contain any animations
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Tab Navigation */}
              <div className="absolute w-[117px] h-8 top-0 left-[228px]">
                <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                  <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />

                  <div className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                    Animations
                  </div>
                </div>
              </div>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[342px] block"
                to="/reviewdexhero"
              >
                <div className="absolute top-[7px] left-2 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[13px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  Review DexHero
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-0 block"
                to="/createbasicinfo"
              >
                <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  Basic Info
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[114px] block"
                to="/create"
              >
                <div className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  3D Model
                </div>
              </Link>

            </div>
          </div>
        </div>

        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />

          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>

          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>

          <img
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
            alt="Createpage"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />

          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>

          <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>

          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>

      </div>
    </div>
  );
};
