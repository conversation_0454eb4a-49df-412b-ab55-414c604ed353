import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDexHero } from "../../contexts/DexHeroContext";
import '@google/model-viewer';

export const Reviewdexhero = () => {
  const { creationData } = useDexHero();
  const [walletAddress, setWalletAddress] = useState('');
  const [tokenSupplyToBuy, setTokenSupplyToBuy] = useState(100000);
  const [currentAnimation, setCurrentAnimation] = useState(null);
  const [showTutorial, setShowTutorial] = useState(false);

  // Get selected animations from context
  const selectedAnimations = creationData.selectedAnimations || [];
  const detectedAnimations = creationData.animations || [];

  // Show tutorial on first visit
  useEffect(() => {
    const hasSeenReviewTutorial = localStorage.getItem('hasSeenReviewTutorial');
    if (!hasSeenReviewTutorial) {
      const timer = setTimeout(() => {
        setShowTutorial(true);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleTutorialClose = () => {
    setShowTutorial(false);
    localStorage.setItem('hasSeenReviewTutorial', 'true');
  };

  // Function to get icon for category
  const getCategoryIcon = (category) => {
    const icons = {
      'Movement': '🚶',
      'Combat': '⚔️',
      'Idle': '🧍',
      'Emotes': '😊',
      'Actions': '🤸',
      'Magic': '✨',
      'Tools': '🔨',
      'Other': '📤'
    };
    return icons[category] || icons['Other'];
  };

  // Handle animation playback
  const playAnimation = (animation) => {
    // If clicking the same animation that's currently playing, stop it
    if (currentAnimation?.id === animation.id) {
      setCurrentAnimation(null);
      stopCurrentAnimation();
      console.log(`Stopping animation: ${animation.name}`);
    } else {
      // Play the new animation
      setCurrentAnimation(animation);
      playAnimationOnModel(animation);
      console.log(`Playing animation: ${animation.name}`);
    }
  };

  // Function to play animation on the model-viewer
  const playAnimationOnModel = (animation) => {
    const modelViewer = document.querySelector('model-viewer');
    if (modelViewer) {
      try {
        // Use model-viewer's animation API
        const animationName = animation.name;
        console.log(`Attempting to play animation: ${animationName}`);
        
        // Set the animation name on the model-viewer
        modelViewer.animationName = animationName;
        
        // Play the animation
        modelViewer.play();
        
        console.log(`Successfully started animation: ${animationName}`);
      } catch (error) {
        console.error('Error playing animation on model:', error);
      }
    } else {
      console.error('Model viewer not found');
    }
  };

  // Function to stop current animation
  const stopCurrentAnimation = () => {
    const modelViewer = document.querySelector('model-viewer');
    if (modelViewer) {
      try {
        // Pause the animation
        modelViewer.pause();
        console.log('Animation stopped');
      } catch (error) {
        console.error('Error stopping animation:', error);
      }
    }
  };

  // If no model file is uploaded, show the no-model layout
  if (!creationData.modelFile) {
    return (
      <div
        className="bg-transparent flex flex-row justify-center w-full"
        data-model-id="1305:16763"
      >
        <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
          <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
            <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
              <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
                <div className="inline-flex items-center gap-[9.09px] relative">
                  <Link
                    className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                    to="/homegamesimg"
                  >
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </Link>
                  <Link
                    className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                    to="/homegamesimg"
                  >
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </Link>
                  <Link
                    className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                    to="/homegamesimg"
                  >
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </Link>
                  <Link
                    className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                    to="/homegamesimg"
                  >
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </Link>
                  <Link
                    className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                    to="/homegamesimg"
                  >
                    <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                      <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                        SPDRSLG
                      </div>
                      <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                        +55%
                      </div>
                      <img
                        className="absolute w-[59px] h-[70px] top-0 left-14"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                      />
                    </div>
                  </Link>
                </div>
              </div>
              <div className="relative w-[100px] h-[21px]">
                <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                  <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                    0x5kb...vl86d
                  </div>
                </div>
              </div>
              <img
                className="relative w-[29.7px] h-[29.7px]"
                alt="Profile"
                src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
              />
            </header>

            <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
              {/* Back Button */}
              <Link
                to="/createanimations"
                className="absolute w-[57px] h-[21px] top-[7px] left-[534px] cursor-pointer z-10"
              >
                <div className="absolute w-[41px] top-0 left-[15px] [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] tracking-[0] leading-[20.5px] hover:text-white transition-colors">
                  Back
                </div>
                <img
                  className="absolute w-[7px] h-[11px] top-[5px] left-0"
                  alt="Vector"
                  src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                />
              </Link>

              {/* Create Button */}
              <Link
                to="/homegamesimg"
                className="absolute w-[70px] h-[21px] top-[7px] left-[605px] cursor-pointer z-10"
                onClick={() => {
                  // TODO: Implement DexHero creation logic
                  console.log('Creating DexHero...', creationData);
                }}
              >
                <div className="w-[50px] top-0 left-0 [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] leading-[20.5px] absolute tracking-[0] hover:text-white transition-colors">
                  Create
                </div>

                <img
                  className="absolute w-[7px] h-[11px] top-[5px] right-0"
                  alt="Vector"
                  src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                  style={{ transform: 'scaleX(-1)' }}
                />
              </Link>

              <div className="absolute w-[675px] h-[440px] top-0 left-0">
                <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

                {/* No Model Uploaded Message */}
                <div className="absolute w-[640px] h-[370px] top-[50px] left-[17px]">
                  <div className="w-full h-full flex items-center justify-center border-2 border-dashed border-[#3a3a3a] rounded-lg">
                    <div className="text-center">
                      <p className="[font-family:'Lato',Helvetica] font-medium text-[#8d8d8d] text-lg mb-2">
                        No 3D Model Uploaded
                      </p>
                      <p className="[font-family:'Lato',Helvetica] font-light text-[#5a5a5a] text-sm">
                        Please upload a 3D model and select animations first
                      </p>
                    </div>
                  </div>
                </div>

                {/* Tab Navigation - Review DexHero tab is active */}
                <div className="absolute w-[117px] h-8 top-0 left-[342px]">
                  <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                    <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />
                    <div className="absolute top-[7px] left-2 [font-family:'Lexend',Helvetica] font-normal text-white text-[13px] tracking-[0] leading-[normal]">
                      Review DexHero
                    </div>
                  </div>
                </div>

              <Link
                className="absolute w-[117px] h-8 top-0 left-0 block"
                to="/createbasicinfo"
              >
                <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  Basic Info
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[114px] block"
                to="/create"
              >
                <div className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  3D Model
                </div>
              </Link>

                <Link
                  className="absolute w-[117px] h-8 top-0 left-[228px] block"
                  to="/createanimations"
                >
                  <div className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                    Animations
                  </div>
                </Link>
              </div>
            </div>
          </div>

          <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
            <img
              className="absolute w-[33px] h-px top-10 left-[5px]"
              alt="Dividingline"
              src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
            />
            
            <Link to="/hometoken">
              <img
                className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
                alt="Tokenpage"
                src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
              />
            </Link>
            
            <Link to="/homegamesimg">
              <img
                className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
                alt="Gamespage"
                src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
              />
            </Link>
            
            <img
              className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
              alt="Createpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
            
            <Link
              className="absolute w-5 h-5 top-[122px] left-[11px] block"
              to="/homeitemimg"
            >
              <img
                className="absolute w-[9px] h-[9px] top-0 left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-0 left-3"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-3 left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
              />
              <img
                className="absolute w-[9px] h-[9px] top-3 left-3"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
              />
            </Link>
            
            <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
              <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
            </div>
            
            <img
              className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
              alt="Dexherologo"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full"
      data-model-id="1305:16763"
    >
      <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
          <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
            <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
              <div className="inline-flex items-center gap-[9.09px] relative">
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>
                    <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>
                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>
                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>
                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>
                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>
                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>
                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>
                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>
                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>
                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
              </div>
            </div>
            <div className="relative w-[100px] h-[21px]">
              <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                  0x5kb...vl86d
                </div>
              </div>
            </div>
            <img
              className="relative w-[29.7px] h-[29.7px]"
              alt="Profile"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </header>

          <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
            {/* Back Button */}
            <Link
              to="/createanimations"
              className="absolute w-[57px] h-[21px] top-[7px] left-[534px] cursor-pointer z-10"
            >
              <div className="absolute w-[41px] top-0 left-[15px] [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] tracking-[0] leading-[20.5px] hover:text-white transition-colors">
                Back
              </div>
              <img
                className="absolute w-[7px] h-[11px] top-[5px] left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
              />
            </Link>

            {/* Create Button */}
            <Link
              to="/homegamesimg"
              className="absolute w-[70px] h-[21px] top-[7px] left-[605px] cursor-pointer z-10"
              onClick={() => {
                // TODO: Implement DexHero creation logic
                console.log('Creating DexHero...', creationData);
              }}
            >
              <div className="w-[50px] top-0 left-0 [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] leading-[20.5px] absolute tracking-[0] hover:text-white transition-colors">
                Create
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] right-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                style={{ transform: 'scaleX(-1)' }}
              />
            </Link>

            <div className="absolute w-[675px] h-[440px] top-0 left-0">
              <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

              {/* Split Layout: 3D Model on Left, Stats/Controls on Right */}
              <div className="absolute w-[640px] h-[370px] top-[50px] left-[17px] flex gap-4">
                
                {/* Left Side - 3D Model Viewer */}
                <div className="w-1/2 h-full flex items-center justify-center relative">
                  {creationData.modelUrl ? (
                    <model-viewer
                      src={creationData.modelUrl}
                      alt="DexHero 3D Model"
                      auto-rotate
                      camera-controls
                      style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'transparent'
                      }}
                      environment-image="neutral"
                      shadow-intensity="1"
                      camera-orbit="0deg 75deg 105%"
                    />
                  ) : (
                    <div className="text-center">
                      <div className="text-4xl mb-2 text-[#8d8d8d]">🎮</div>
                      <div className="text-[#8d8d8d] text-sm">No Model Uploaded</div>
                    </div>
                  )}
                  
                </div>

                {/* Right Side - Stats and Controls */}
                <div className="w-1/2 h-full flex flex-col">
                  
                  {/* DexHero Header */}
                  <div className="mb-1 flex-shrink-0">
                    <div className="flex items-center justify-between mb-1">
                      <h2 className="text-lg font-bold text-white">
                        {creationData.basicInfo?.name || 'Unnamed DexHero'}
                      </h2>
                      <span className="text-white font-bold text-sm bg-[#1a1a1a] border border-[#3a3a3a] rounded px-2 py-1">
                        {creationData.basicInfo?.ticker || 'HERO'}
                      </span>
                    </div>
                    <div className="text-xs text-[#8d8d8d]">
                      @{creationData.basicInfo?.collection || 'DexHeroCollection'} {creationData.basicInfo?.blockchain || 'Solana'}
                    </div>
                  </div>

                  {/* Description */}
                  <div className="mb-1 flex-shrink-0">
                    <div className="text-xs">
                      <div>
                        <span className="text-[#8d8d8d]">Description:</span>
                        <div 
                          className="text-white text-xs mt-1 leading-tight overflow-y-auto"
                          style={{ 
                            height: '3.6em', // Exactly 3 lines (1.2em line-height * 3)
                            lineHeight: '1.2em'
                          }}
                        >
                          {creationData.basicInfo?.description || 'A unique DexHero NFT with special abilities and animations.'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Animations Action Bar */}
                  <div className="mb-2 flex-shrink-0">
                    <h3 className="text-white font-bold mb-1 text-xs">Selected Animations</h3>
                    <div className="bg-[#1a1a1a] border border-[#3a3a3a] rounded-lg p-2 relative">
                      <div className="flex items-center space-x-2 overflow-x-auto pb-1" style={{ scrollbarWidth: 'thin', scrollbarColor: '#3a3a3a #1a1a1a' }}>
                        {selectedAnimations.length > 0 ? (
                          detectedAnimations
                            .filter(anim => selectedAnimations.includes(anim.id))
                            .map((animation, index) => {
                              const isPlaying = currentAnimation?.id === animation.id;
                              return (
                                <div key={animation.id} className="flex-shrink-0 relative group">
                                  <button
                                    onClick={() => playAnimation(animation)}
                                    className={`
                                      w-8 h-8 rounded-lg border-2 transition-all duration-200 flex items-center justify-center
                                      ${isPlaying 
                                        ? 'border-[#08f7fe] bg-[#08f7fe] bg-opacity-30 shadow-lg shadow-[#08f7fe]/30' 
                                        : 'border-[#08f7fe] bg-[#08f7fe] bg-opacity-10 hover:bg-opacity-20'
                                      }
                                    `}
                                  >
                                    {animation.hasCustomIcon && animation.iconDataUrl ? (
                                      <img
                                        src={animation.iconDataUrl}
                                        alt={animation.customName || animation.name}
                                        className="w-6 h-6 object-cover rounded"
                                        style={{ imageRendering: 'crisp-edges' }}
                                      />
                                    ) : (
                                      <span className="text-sm">
                                        {getCategoryIcon(animation.customCategory || animation.category)}
                                      </span>
                                    )}
                                    
                                    {/* Playing indicator */}
                                    {isPlaying && (
                                      <div className="absolute inset-0 rounded-lg border-2 border-[#08f7fe] animate-pulse"></div>
                                    )}
                                  </button>
                                  
                                  {/* Hover Tooltip */}
                                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 pointer-events-none">
                                    <div className="bg-black bg-opacity-90 backdrop-blur-sm rounded-lg border border-[#3a3a3a] p-2 shadow-xl whitespace-nowrap">
                                      <div className="text-white text-xs font-medium mb-1">
                                        {animation.customName || animation.name.replace('Character_', '')}
                                      </div>
                                      <div className="text-[#8d8d8d] text-xs">
                                        {animation.customCategory || animation.category}
                                      </div>
                                      <div className="text-[#08f7fe] text-xs mt-1">
                                        {isPlaying ? 'Click to stop' : 'Click to play'}
                                      </div>
                                    </div>
                                    
                                    {/* Tooltip Arrow */}
                                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                                  </div>
                                </div>
                              );
                            })
                        ) : (
                          <div className="text-center text-[#8d8d8d] text-xs py-1">
                            No animations selected
                          </div>
                        )}
                      </div>
                      
                    </div>
                  </div>

                  {/* Wallet Address Input */}
                  <div className="mb-2 flex-shrink-0">
                    <label className="block text-white font-bold mb-1 text-xs">
                      Wallet Address
                    </label>
                    <input
                      type="text"
                      value={walletAddress}
                      onChange={(e) => setWalletAddress(e.target.value)}
                      placeholder="Enter your Solana wallet address..."
                      className="w-full bg-[#1a1a1a] border border-[#3a3a3a] rounded-lg px-2 py-1 text-white placeholder-[#8d8d8d] focus:border-[#08f7fe] focus:outline-none text-xs"
                    />
                  </div>

                  {/* Token Supply Section */}
                  <div className="mb-2 flex-shrink-0">
                    <label className="block text-white font-bold mb-1 text-xs">
                      Initial Token Purchase
                    </label>
                    <div className="bg-[#1a1a1a] border border-[#3a3a3a] rounded-lg p-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-[#8d8d8d] text-xs">Amount:</span>
                        <span className="text-white font-bold text-xs">{tokenSupplyToBuy.toLocaleString()} tokens</span>
                      </div>
                      <input
                        type="range"
                        min="10000"
                        max="1000000"
                        step="10000"
                        value={tokenSupplyToBuy}
                        onChange={(e) => setTokenSupplyToBuy(parseInt(e.target.value))}
                        className="w-full h-1 bg-[#3a3a3a] rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, #08f7fe 0%, #08f7fe ${((tokenSupplyToBuy - 10000) / (1000000 - 10000)) * 100}%, #3a3a3a ${((tokenSupplyToBuy - 10000) / (1000000 - 10000)) * 100}%, #3a3a3a 100%)`
                        }}
                      />
                      <div className="flex justify-between text-xs text-[#8d8d8d] mt-1">
                        <span>10K</span>
                        <span>1M</span>
                      </div>
                    </div>
                  </div>

                  {/* Mint Button */}
                  <button
                    onClick={() => {
                      console.log('Minting DexHero with:', {
                        name: creationData.name,
                        walletAddress,
                        tokenSupplyToBuy,
                        selectedAnimations,
                        modelUrl: creationData.modelUrl
                      });
                      alert(`DexHero "${creationData.name || 'Unnamed'}" ready to mint!\n\nWallet: ${walletAddress}\nTokens: ${tokenSupplyToBuy.toLocaleString()}\nAnimations: ${selectedAnimations.length}`);
                    }}
                    disabled={!walletAddress || selectedAnimations.length === 0}
                    className={`w-full py-2 rounded-lg font-bold text-xs transition-all duration-300 flex-shrink-0 ${
                      walletAddress && selectedAnimations.length > 0
                        ? 'bg-gradient-to-r from-[#08f7fe] to-[#0074e0] text-black hover:from-[#0074e0] hover:to-[#08f7fe] transform hover:scale-105 shadow-lg'
                        : 'bg-[#3a3a3a] text-[#8d8d8d] cursor-not-allowed'
                    }`}
                  >
                    {!walletAddress ? 'Enter Wallet Address' : 
                     selectedAnimations.length === 0 ? 'Select Animations' : 
                     'Mint DexHero NFT'}
                  </button>
                </div>
              </div>

              {/* Tab Navigation - Review DexHero tab is active */}
              <div className="absolute w-[117px] h-8 top-0 left-[342px]">
                <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                  <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />
                  <div className="absolute top-[7px] left-2 [font-family:'Lexend',Helvetica] font-normal text-white text-[13px] tracking-[0] leading-[normal]">
                    Review DexHero
                  </div>
                </div>
              </div>

              <Link
                className="absolute w-[117px] h-8 top-0 left-0 block"
                to="/createbasicinfo"
              >
                <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  Basic Info
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[114px] block"
                to="/create"
              >
                <div className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  3D Model
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[228px] block"
                to="/createanimations"
              >
                <div className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal] hover:text-white transition-colors">
                  Animations
                </div>
              </Link>
            </div>
          </div>
        </div>

        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />
          
          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>
          
          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>
          
          <img
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
            alt="Createpage"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
          
          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
            />
            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>
          
          <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>
          
          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>
      </div>

      {/* Review Tutorial Popup */}
      {showTutorial && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gradient-to-br from-[#1a1a1a] to-[#0d1117] border-2 border-[#08f7fe]/30 rounded-xl p-6 max-w-md mx-4 shadow-2xl">
            {/* Header with gaming accent */}
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] rounded-lg flex items-center justify-center mr-3">
                <span className="text-black font-bold text-lg">🎯</span>
              </div>
              <h3 className="[font-family:'Lexend',Helvetica] text-white text-xl font-semibold">
                Review Your DexHero
              </h3>
            </div>

            {/* Instruction */}
            <p className="[font-family:'Lato',Helvetica] text-[#e6edf3] text-base mb-6 leading-relaxed text-center">
              Review your DexHero's details, test animations, and finalize your creation before deployment.
            </p>

            {/* Action button */}
            <div className="flex justify-center">
              <button
                onClick={handleTutorialClose}
                className="px-8 py-3 bg-gradient-to-r from-[#08f7fe] to-[#0074e0] hover:from-[#06d4e0] hover:to-[#005bb5] text-black font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <span className="[font-family:'Lexend',Helvetica]">GOT IT</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
