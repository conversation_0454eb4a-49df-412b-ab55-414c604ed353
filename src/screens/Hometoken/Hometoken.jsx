import React from "react";
import { Link } from "react-router-dom";

export const Hometoken = () => {
  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full"
      data-model-id="1343:12946"
    >
      <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />

          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>

          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>

          <Link
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px] block"
            to="/createbasicinfo"
          >
            <div className="relative h-[19px]">
              <img
                className="absolute w-0.5 h-0.5 top-1.5 left-[5px]"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-98.svg"
              />

              <img
                className="absolute w-[17px] h-[17px] top-0 left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-99.svg"
              />

              <img
                className="absolute w-3 h-[11px] top-2 left-[5px]"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-100.svg"
              />
            </div>
          </Link>

          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-84.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-86.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>

          <div className="absolute w-[3px] h-[13px] top-[184px] left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>

          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>

        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] overflow-hidden overflow-y-scroll">
          <div className="w-[709px] h-[1858px]">
            <div className="relative h-[1060px] bg-black">
              <div className="absolute w-[438px] h-[300px] top-[87px] left-10">
                <img
                  className="absolute w-[259px] h-[293px] top-px left-[179px] object-cover"
                  alt="Modelview"
                  src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                />

                <div className="absolute w-[191px] top-[31px] left-0 [font-family:'Vanguard_CF-DemiBold',Helvetica] font-bold text-gray-50 text-[60px] tracking-[-2px] leading-[55px]">
                  GREEN <br />
                  GOBLIN
                </div>

                <p className="absolute w-[191px] top-[172px] left-0 [font-family:'Inter',Helvetica] font-normal text-[#cfd4dc] text-[8.9px] tracking-[0] leading-[14.9px]">
                  Green Goblin is a Marvel Series Outfit in Fortnite: Battle
                  Royale
                </p>

                <div className="absolute w-[39px] h-[39px] top-[234px] left-[21px]">
                  <div className="relative h-[39px] rounded-[19.44px]">
                    <img
                      className="absolute w-3.5 h-3.5 top-3 left-3"
                      alt="Icon"
                      src="https://c.animaapp.com/Hxjzzvpo/img/icon-10.svg"
                    />

                    <div className="absolute w-[39px] h-[39px] top-0 left-0 rounded-[19.44px] border-[0.97px] border-solid border-white" />
                  </div>
                </div>

                <div className="absolute w-[39px] h-[39px] top-[234px] left-[100px]">
                  <div className="relative h-[39px] rounded-[19.44px]">
                    <img
                      className="absolute w-3.5 h-3.5 top-3 left-3"
                      alt="Icon"
                      src="https://c.animaapp.com/Hxjzzvpo/img/icon-11.svg"
                    />

                    <div className="w-[39px] h-[39px] rounded-[19.44px] border-[0.97px] border-solid absolute top-0 left-0 border-white rotate-180" />
                  </div>
                </div>
              </div>

              <img
                className="absolute w-[89px] h-4 top-[59px] left-10"
                alt="Collectionname"
                src="https://c.animaapp.com/Hxjzzvpo/img/collectionname-5.svg"
              />

              <div className="absolute top-[55px] left-[274px] [font-family:'Inter',Helvetica] font-medium text-[#cfd4dc] text-[14.7px] tracking-[0] leading-[22.0px] whitespace-nowrap">
                Pricing
              </div>

              <div className="absolute top-[55px] left-[165px] [font-family:'Inter',Helvetica] font-medium text-gray-50 text-[14.7px] tracking-[0] leading-[22.0px] whitespace-nowrap">
                Collection
              </div>

              <div className="absolute w-[194px] top-[117px] left-[488px] [font-family:'Inter',Helvetica] font-semibold text-warning-500 text-xs tracking-[0] leading-[11.9px]">
                DexHero’s
              </div>

              <div className="absolute w-[194px] top-[138px] left-[488px] [font-family:'Bakbak_One',Helvetica] font-normal text-gray-50 text-[23.9px] tracking-[-0.48px] leading-[normal]">
                Character Token
              </div>

              <p className="absolute w-[175px] top-[180px] left-[488px] [font-family:'Inter',Helvetica] font-normal text-[#cfd4dc] text-[8.9px] tracking-[0] leading-[14.9px]">
                A percentage of every transaction burns a portion of the
                DexHero’s native token
              </p>

              <div className="absolute w-[58px] h-[72px] top-[255px] left-[562px]">
                <div className="w-14 top-[46px] left-0 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[12.8px] text-center leading-[11.5px] absolute tracking-[0]">
                  Play
                </div>

                <div className="absolute w-[33px] h-[33px] top-0 left-[11px] rounded-[16.5px]">
                  <div className="w-[33px] h-[33px] rounded-[16.5px] border-[0.82px] border-solid absolute top-0 left-0 border-white rotate-180" />

                  <img
                    className="absolute w-[13px] h-[15px] top-2 left-[11px]"
                    alt="Button play button"
                    src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-0.5 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-60.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-60.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-3 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-46.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-46.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-[23px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-47.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-47.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-[33px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-48.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-48.svg"
                  />
                </div>

                <div className="absolute w-2.5 h-2.5 top-[62px] left-11 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-49.svg)] bg-[100%_100%]">
                  <img
                    className="w-2.5 h-2.5 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-49.svg"
                  />
                </div>
              </div>

              <div className="absolute w-[41px] h-[55px] top-[272px] left-[620px]">
                <div className="top-[35px] left-1.5 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[9.8px] text-center leading-[8.7px] whitespace-nowrap absolute tracking-[0]">
                  BUILD
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-0 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-80.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-80.svg"
                  />
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-2 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-81.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-81.svg"
                  />
                </div>

                <div className="absolute w-2 h-2 top-[47px] left-4 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-52.svg)] bg-[100%_100%]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-52.svg"
                  />
                </div>

                <div className="absolute w-[15px] h-2 top-[47px] left-[23px]">
                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star background"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-background-53.svg"
                  />

                  <img
                    className="w-2 h-2 left-0 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-53.svg"
                  />

                  <img
                    className="w-2 h-2 left-2 absolute top-0"
                    alt="Star background"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-background-84.svg"
                  />

                  <img
                    className="w-2 h-2 left-2 absolute top-0"
                    alt="Star"
                    src="https://c.animaapp.com/Hxjzzvpo/img/star-84.svg"
                  />
                </div>

                <div className="absolute w-[26px] h-[26px] top-0 left-[7px] rounded-[13px]">
                  <div className="w-[26px] h-[26px] rounded-[13px] border-[0.65px] border-solid absolute top-0 left-0 border-white rotate-180" />

                  <img
                    className="absolute w-3 h-[13px] top-[7px] left-[7px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-88.svg"
                  />
                </div>
              </div>

              <div className="absolute w-[72px] h-[92px] top-[235px] left-[488px]">
                <div className="relative w-[74px] h-[92px]">
                  <div className="absolute w-[74px] h-[92px] top-0 left-0">
                    <div className="w-[72px] top-[59px] left-0 [font-family:'Inter',Helvetica] font-semibold text-[#cfd4dc] text-[16.5px] text-center leading-[14.7px] absolute tracking-[0]">
                      Trade
                    </div>

                    <div className="absolute w-[43px] h-[43px] top-0 left-[15px] rounded-[21.41px] border-[1.07px] border-solid border-white rotate-180" />

                    <div className="absolute w-[13px] h-[13px] top-[79px] left-[3px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-55.svg)] bg-[100%_100%]">
                      <img
                        className="w-[13px] h-[13px] left-0 absolute top-0"
                        alt="Star"
                        src="https://c.animaapp.com/Hxjzzvpo/img/star-55.svg"
                      />
                    </div>

                    <div className="absolute w-[13px] h-[13px] top-[79px] left-4 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-71.svg)] bg-[100%_100%]">
                      <img
                        className="w-[13px] h-[13px] left-0 absolute top-0"
                        alt="Star"
                        src="https://c.animaapp.com/Hxjzzvpo/img/star-71.svg"
                      />
                    </div>

                    <div className="absolute w-[13px] h-[13px] top-[79px] left-[29px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-57.svg)] bg-[100%_100%]">
                      <img
                        className="w-[13px] h-[13px] left-0 absolute top-0"
                        alt="Star"
                        src="https://c.animaapp.com/Hxjzzvpo/img/star-57.svg"
                      />
                    </div>

                    <div className="absolute w-[13px] h-[13px] top-[79px] left-[43px] bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-58.svg)] bg-[100%_100%]">
                      <img
                        className="w-[13px] h-[13px] left-0 absolute top-0"
                        alt="Star"
                        src="https://c.animaapp.com/Hxjzzvpo/img/star-58.svg"
                      />
                    </div>

                    <div className="absolute w-[13px] h-[13px] top-[79px] left-14 bg-[url(https://c.animaapp.com/Hxjzzvpo/img/star-background-74.svg)] bg-[100%_100%]">
                      <img
                        className="w-[13px] h-[13px] left-0 absolute top-0"
                        alt="Star"
                        src="https://c.animaapp.com/Hxjzzvpo/img/star-74.svg"
                      />
                    </div>
                  </div>

                  <img
                    className="absolute w-[21px] h-px top-[30px] left-[25px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-89.svg"
                  />

                  <img
                    className="absolute w-[5px] h-2.5 top-5 left-7"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-90.svg"
                  />

                  <img
                    className="absolute w-[5px] h-[19px] top-[11px] left-[38px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-91.svg"
                  />

                  <img
                    className="absolute w-[5px] h-[15px] top-[15px] left-[33px]"
                    alt="Vector"
                    src="https://c.animaapp.com/Hxjzzvpo/img/vector-92.svg"
                  />
                </div>
              </div>

              <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
                <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
                  <div className="inline-flex items-center gap-[9.09px] relative">
                    <Link
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                      to="/homegamesimg"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-3 left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </Link>

                    <Link
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                      to="/homegamesimg"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </Link>

                    <Link
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                      to="/homegamesimg"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </Link>

                    <Link
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                      to="/homegamesimg"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </Link>

                    <Link
                      className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                      to="/homegamesimg"
                    >
                      <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                        <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                          SPDRSLG
                        </div>

                        <div className="top-[13px] left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] leading-[11.2px] whitespace-nowrap absolute tracking-[0]">
                          +55%
                        </div>

                        <img
                          className="absolute w-[59px] h-[70px] top-0 left-14"
                          alt="Vector"
                          src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                        />
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="relative w-[100px] h-[21px]">
                  <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                    <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                      0x5kb...vl86d
                    </div>
                  </div>
                </div>

                <img
                  className="relative w-[29.7px] h-[29.7px]"
                  alt="Profile"
                  src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                />
              </header>

              <div className="absolute w-[677px] h-[627px] top-[396px] left-[11px]">
                <div className="relative w-[675px] h-[627px]">
                  <Link
                    className="absolute top-0.5 left-[604px] [font-family:'Asap',Helvetica] font-bold text-white text-xs text-center tracking-[0] leading-[20.6px] whitespace-nowrap block"
                    to="/createbasicinfo"
                  >
                    CREATE
                  </Link>

                  <div className="absolute w-[675px] h-[627px] top-0 left-0">
                    <div className="absolute w-[675px] h-[596px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

                    <Link
                      className="absolute w-[117px] h-8 top-0 left-0 block"
                      to="/homegamesimg"
                    >
                      <div className="absolute top-1.5 left-8 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                        Games
                      </div>
                    </Link>

                    <Link
                      className="absolute w-[117px] h-8 top-0 left-[114px] block"
                      to="/homeitemimg"
                    >
                      <div className="absolute top-[7px] left-9 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                        Items
                      </div>
                    </Link>

                    <div className="absolute w-[117px] h-8 top-0 left-[228px]">
                      <div className="relative w-[115px] h-8 bg-black rounded-[8px_8px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#6a6f75]">
                        <div className="absolute top-1.5 left-[35px] [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                          Token
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
