import React, { useState, useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDexHero } from "../../contexts/DexHeroContext";
import '@google/model-viewer';

export const Create = () => {
  const { 
    creationData, 
    setModelFile, 
    validateModelUpload,
    updateCreationProgress 
  } = useDexHero();

  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [fileInfo, setFileInfo] = useState(null);
  const fileInputRef = useRef(null);

  // Handle file validation
  const validateFile = (file) => {
    const maxSize = 500 * 1024 * 1024; // 500MB
    const allowedTypes = ['.glb'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      throw new Error('Only .glb files are allowed');
    }

    if (file.size > maxSize) {
      throw new Error('File size must be under 500MB');
    }

    return true;
  };

  // Handle file upload
  const handleFileUpload = async (file) => {
    try {
      validateFile(file);
      setIsUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Create object URL for preview
      const modelUrl = URL.createObjectURL(file);
      
      // Set file info
      const info = {
        name: file.name,
        size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
        type: file.type || 'model/gltf-binary'
      };
      setFileInfo(info);

      // Complete upload
      setTimeout(() => {
        setUploadProgress(100);
        setModelFile(file, modelUrl);
        validateModelUpload();
        setIsUploading(false);
        clearInterval(progressInterval);
      }, 1000);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      alert(error.message);
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // File input handler
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // Browse button handler
  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const hasModel = creationData.modelFile || creationData.modelUrl;
  return (
    <div
      className="bg-transparent flex flex-row justify-center w-full"
      data-model-id="1305:16617"
    >
      <div className="overflow-x-hidden w-[751.76px] h-[508px] relative">
        <div className="absolute w-[709px] h-[508px] top-0 left-[43px] bg-black">
          <header className="inline-flex items-center gap-1.5 absolute top-1.5 left-[15px] bg-transparent">
            <div className="relative w-[549px] h-[23px] overflow-hidden overflow-x-scroll">
              <div className="inline-flex items-center gap-[9.09px] relative">
                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-5 absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-3 absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>

                <Link
                  className="relative w-[111.98px] h-[23.45px] rounded-[5.86px] overflow-hidden block"
                  to="/homegamesimg"
                >
                  <div className="relative w-[115px] h-[70px] top-[-13px] left-[3px]">
                    <div className="top-[21px] absolute left-0 [font-family:'Manrope',Helvetica] font-medium text-white text-[8px] tracking-[0] leading-[8px] whitespace-nowrap">
                      SPDRSLG
                    </div>

                    <div className="top-[13px] absolute left-[34px] [font-family:'Manrope',Helvetica] font-bold text-primarygreen500-success-color text-[8px] tracking-[0] leading-[11.2px] whitespace-nowrap">
                      +55%
                    </div>

                    <img
                      className="absolute w-[59px] h-[70px] top-0 left-14"
                      alt="Vector"
                      src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
                    />
                  </div>
                </Link>
              </div>
            </div>

            <div className="relative w-[100px] h-[21px]">
              <div className="relative w-[98px] h-[21px] rounded-[2.05px] border-[1.02px] border-solid border-[#2e2e3e]">
                <div className="absolute top-[5px] left-3.5 [font-family:'Roboto',Helvetica] font-medium text-[#69698e] text-[10.2px] text-center tracking-[0] leading-[11.3px] whitespace-nowrap">
                  0x5kb...vl86d
                </div>
              </div>
            </div>

            <img
              className="relative w-[29.7px] h-[29.7px]"
              alt="Profile"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </header>

          <div className="absolute w-[675px] h-[440px] top-[52px] left-[7px]">
            {/* Back Button */}
            <Link
              to="/createbasicinfo"
              className="absolute w-[57px] h-[21px] top-[7px] left-[547px] cursor-pointer z-10"
            >
              <div className="absolute w-[41px] top-0 left-[15px] [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] tracking-[0] leading-[20.5px] hover:text-white transition-colors">
                Back
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] left-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
              />
            </Link>

            {/* Next Button */}
            <Link
              to="/createanimations"
              className="absolute w-[57px] h-[21px] top-[7px] left-[618px] cursor-pointer z-10"
            >
              <div className="w-[41px] top-0 left-0 [font-family:'Asap',Helvetica] font-bold text-[#d8d8e7] text-[18.6px] leading-[20.5px] absolute tracking-[0] hover:text-white transition-colors">
                Next
              </div>

              <img
                className="absolute w-[7px] h-[11px] top-[5px] right-0"
                alt="Vector"
                src="https://c.animaapp.com/Hxjzzvpo/img/vector-44.svg"
                style={{ transform: 'scaleX(-1)' }}
              />
            </Link>

            <div className="absolute w-[675px] h-[440px] top-0 left-0">
              <div className="absolute w-[675px] h-[409px] top-[31px] left-0 rounded-lg border border-solid border-[#6a6f75]" />

              <div className="absolute w-[117px] h-8 top-0 left-[114px]">
                <div className="relative w-[115px] h-8 rounded-[3.78px_3.78px_0px_0px]">
                  <div className="absolute w-[115px] h-8 top-0 left-0 bg-black rounded-[3.78px_3.78px_0px_0px] border-t [border-top-style:solid] border-r [border-right-style:solid] border-l [border-left-style:solid] border-[#62676c] rotate-180" />

                  <div className="absolute top-[7px] left-[23px] [font-family:'Lexend',Helvetica] font-normal text-white text-[15.1px] tracking-[0] leading-[normal]">
                    3D Model
                  </div>
                </div>
              </div>

              <div 
                className={`absolute w-[675px] h-[409px] top-[31px] left-0 transition-all duration-200 ${
                  !hasModel && isDragOver ? 'bg-[#1a1a1a] border-2 border-dashed border-[#08f7fe]' : ''
                } ${!hasModel ? 'cursor-pointer' : 'cursor-default'}`}
                onDragOver={!hasModel ? handleDragOver : undefined}
                onDragLeave={!hasModel ? handleDragLeave : undefined}
                onDrop={!hasModel ? handleDrop : undefined}
                onClick={!hasModel ? handleBrowseClick : undefined}
                id="3dmodelviewwindow"
              >
                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".glb"
                  onChange={handleFileSelect}
                  className="hidden"
                />

                {!hasModel && !isUploading && (
                  <>
                    <div className="absolute w-[59px] h-[57px] top-[156px] left-[308px]">
                      <img
                        className="absolute w-1 h-[25px] top-5 left-[27px]"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-34.svg"
                      />

                      <img
                        className="absolute w-[25px] h-1 top-[31px] left-[17px]"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-2-1.svg"
                      />

                      <img
                        className="absolute w-[59px] h-[57px] top-0 left-0"
                        alt="Vector"
                        src="https://c.animaapp.com/Hxjzzvpo/img/vector-3-1.svg"
                      />
                    </div>

                    <p className="absolute top-[246px] left-[183px] [font-family:'Lato',Helvetica] font-normal text-white text-xl tracking-[0] leading-[normal] whitespace-nowrap">
                      {isDragOver ? 'Drop Your .glb File Here' : 'Drag And Drop Your .glb File Here'}
                    </p>

                    <p className="absolute top-[294px] left-[240px] [font-family:'Lato',Helvetica] font-light text-white text-[15px] tracking-[0] leading-[normal] whitespace-nowrap">
                      Only .glb Files Up To 500mb
                    </p>

                    <div className="absolute top-[354px] left-[305px] [font-family:'Lato',Helvetica] font-normal text-[#369ad3] text-xl tracking-[0] leading-[normal] underline whitespace-nowrap hover:text-[#08f7fe] transition-colors">
                      Browse
                    </div>
                  </>
                )}

                {isUploading && (
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <div className="w-[200px] h-2 bg-[#2a2a2a] rounded-full mb-4">
                      <div 
                        className="h-full bg-[#08f7fe] rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                    <p className="[font-family:'Lato',Helvetica] font-normal text-white text-lg">
                      Uploading... {uploadProgress}%
                    </p>
                  </div>
                )}

                {hasModel && !isUploading && (
                  <div className="absolute inset-0">
                    <model-viewer
                      src={creationData.modelUrl}
                      alt="3D Model Preview"
                      auto-rotate
                      camera-controls
                      style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'transparent'
                      }}
                      environment-image="neutral"
                      shadow-intensity="1"
                      camera-orbit="0deg 75deg 105%"
                    />
                    
                    {/* Replace file button */}
                    <div className="absolute top-2 right-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBrowseClick();
                        }}
                        className="bg-[#369ad3] hover:bg-[#08f7fe] text-white text-xs px-2 py-1 rounded transition-colors"
                      >
                        Replace
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <Link
                className="absolute w-[117px] h-8 top-0 left-0 block"
                to="/createbasicinfo"
              >
                <div className="absolute top-[7px] left-5 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  Basic Info
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[228px] block"
                to="/createanimations"
              >
                <div className="absolute top-[7px] left-4 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[15.1px] tracking-[0] leading-[normal]">
                  Animations
                </div>
              </Link>

              <Link
                className="absolute w-[117px] h-8 top-0 left-[342px] block"
                to="/reviewdexhero"
              >
                <div className="absolute top-[7px] left-2 [font-family:'Lexend',Helvetica] font-normal text-[#8d8d8d] text-[13px] tracking-[0] leading-[normal]">
                  Review DexHero
                </div>
              </Link>
            </div>
          </div>
        </div>

        <div className="absolute w-[43px] h-[508px] top-0 left-0 bg-black border-r-[0.5px] [border-right-style:solid] border-[#6a6f75]">
          <img
            className="absolute w-[33px] h-px top-10 left-[5px]"
            alt="Dividingline"
            src="https://c.animaapp.com/Hxjzzvpo/img/dividingline-8.svg"
          />

          <Link to="/hometoken">
            <img
              className="absolute w-[19px] h-[19px] top-[181px] left-3 block"
              alt="Tokenpage"
              src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
            />
          </Link>

          <Link to="/homegamesimg">
            <img
              className="absolute w-[19px] h-3.5 top-[69px] left-3 block"
              alt="Gamespage"
              src="https://c.animaapp.com/Hxjzzvpo/img/gamespage-7.svg"
            />
          </Link>

          <img
            className="absolute w-[17px] h-[19px] top-[237px] left-[13px]"
            alt="Createpage"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />

          <Link
            className="absolute w-5 h-5 top-[122px] left-[11px] block"
            to="/homeitemimg"
          >
            <img
              className="absolute w-[9px] h-[9px] top-0 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-40.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-0 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-85.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-0"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-42.svg"
            />

            <img
              className="absolute w-[9px] h-[9px] top-3 left-3"
              alt="Vector"
              src="https://c.animaapp.com/Hxjzzvpo/img/vector-87.svg"
            />
          </Link>

          <div className="absolute w-[3px] h-[13px] top-60 left-0 bg-[#08f7fe] blur-[3.07px]">
            <div className="h-[13px] bg-[#0074e0] rounded-[0px_9.22px_9.22px_0px]" />
          </div>

          <img
            className="absolute w-[30px] h-[30px] top-[5px] left-[7px] object-cover"
            alt="Dexherologo"
            src="https://c.animaapp.com/Hxjzzvpo/img/<EMAIL>"
          />
        </div>
      </div>
    </div>
  );
};
