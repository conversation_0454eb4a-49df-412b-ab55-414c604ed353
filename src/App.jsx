import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON><PERSON>er<PERSON><PERSON><PERSON>, createHashRouter } from "react-router-dom";
import { DexHeroProvider } from "./contexts/DexHeroContext";
import { Create } from "./screens/Create";
import { Createanimations } from "./screens/Createanimations";
import { Createbasicinfo } from "./screens/Createbasicinfo";
import { Homegamesimg } from "./screens/Homegamesimg";
import { Homegameslist } from "./screens/Homegameslist";
import { Homeitemimg } from "./screens/Homeitemimg";
import { Homeitemlist } from "./screens/Homeitemlist";
import { Hometoken } from "./screens/Hometoken";
import { Reviewdexhero } from "./screens/Reviewdexhero";

// Use HashRouter for browser extension compatibility
const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;

const routes = [
  {
    path: "/",
    element: <Homegamesimg />,
  },
  {
    path: "/homegamesimg",
    element: <Homegamesimg />,
  },
  {
    path: "/homeitemlist",
    element: <Homeitemlist />,
  },
  {
    path: "/createanimations",
    element: <Createanimations />,
  },
  {
    path: "/create3dmodel",
    element: <Create />,
  },
  {
    path: "/create",
    element: <Create />,
  },
  {
    path: "/createbasicinfo",
    element: <Createbasicinfo />,
  },
  {
    path: "/reviewdexhero",
    element: <Reviewdexhero />,
  },
  {
    path: "/hometoken",
    element: <Hometoken />,
  },
  {
    path: "/homeitemimg",
    element: <Homeitemimg />,
  },
  {
    path: "/homegameslist",
    element: <Homegameslist />,
  },
];

// Create router based on context (extension vs web)
const router = isExtension 
  ? createHashRouter(routes)
  : createBrowserRouter(routes);

export const App = () => {
  return (
    <DexHeroProvider>
      <div className="min-h-screen bg-gray-50">
        <RouterProvider router={router} />
      </div>
    </DexHeroProvider>
  );
};
