import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { DexHeroService, GameService, ItemService } from '../services/dexheroService';
import { ChromeExtensionService, initializeExtensionContext } from '../services/chromeExtensionService';

// Initial state
const initialState = {
  dexheroes: [],
  games: [],
  items: [],
  loading: false,
  error: null,
  filters: {
    blockchain: '',
    creator: '',
    tags: [],
    type: 'all' // 'all', 'dexheroes', 'games', 'items'
  },
  viewMode: 'grid', // 'grid', 'list'
  isExtensionContext: false,
  networkStatus: true,
  settings: {
    autoSync: true,
    notifications: true,
    defaultBlockchain: 'solana'
  },
  // DexHero creation state
  creationData: {
    basicInfo: {
      name: '',
      blockchain: 'solana',
      collection: '',
      ticker: '',
      description: ''
    },
    modelFile: null,
    modelUrl: '',
    animations: [],
    selectedAnimations: [],
    animationCustomizations: {},
    marketingData: {
      positioning: 'center',
      scale: 1.0,
      rotation: 0
    }
  },
  creationStep: 'basicInfo', // 'basicInfo', '3dModel', 'animations', 'marketing', 'creation'
  creationProgress: {
    basicInfo: false,
    modelUpload: false,
    animations: false,
    marketing: false
  }
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_DEXHEROES: 'SET_DEXHEROES',
  ADD_DEXHERO: 'ADD_DEXHERO',
  UPDATE_DEXHERO: 'UPDATE_DEXHERO',
  DELETE_DEXHERO: 'DELETE_DEXHERO',
  SET_GAMES: 'SET_GAMES',
  ADD_GAME: 'ADD_GAME',
  SET_ITEMS: 'SET_ITEMS',
  ADD_ITEM: 'ADD_ITEM',
  SET_FILTERS: 'SET_FILTERS',
  SET_VIEW_MODE: 'SET_VIEW_MODE',
  SET_EXTENSION_CONTEXT: 'SET_EXTENSION_CONTEXT',
  SET_NETWORK_STATUS: 'SET_NETWORK_STATUS',
  SET_SETTINGS: 'SET_SETTINGS',
  // Creation flow actions
  UPDATE_CREATION_DATA: 'UPDATE_CREATION_DATA',
  SET_CREATION_STEP: 'SET_CREATION_STEP',
  UPDATE_CREATION_PROGRESS: 'UPDATE_CREATION_PROGRESS',
  RESET_CREATION_DATA: 'RESET_CREATION_DATA',
  SET_MODEL_FILE: 'SET_MODEL_FILE',
  SET_ANIMATIONS: 'SET_ANIMATIONS',
  UPDATE_ANIMATION_CUSTOMIZATIONS: 'UPDATE_ANIMATION_CUSTOMIZATIONS',
  UPDATE_MARKETING_DATA: 'UPDATE_MARKETING_DATA'
};

// Reducer
const dexheroReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    case ActionTypes.SET_DEXHEROES:
      return { ...state, dexheroes: action.payload, loading: false };
    
    case ActionTypes.ADD_DEXHERO:
      return { 
        ...state, 
        dexheroes: [action.payload, ...state.dexheroes],
        loading: false 
      };
    
    case ActionTypes.UPDATE_DEXHERO:
      return {
        ...state,
        dexheroes: state.dexheroes.map(dexhero =>
          dexhero.id === action.payload.id ? action.payload : dexhero
        ),
        loading: false
      };
    
    case ActionTypes.DELETE_DEXHERO:
      return {
        ...state,
        dexheroes: state.dexheroes.filter(dexhero => dexhero.id !== action.payload),
        loading: false
      };
    
    case ActionTypes.SET_GAMES:
      return { ...state, games: action.payload, loading: false };
    
    case ActionTypes.ADD_GAME:
      return { 
        ...state, 
        games: [action.payload, ...state.games],
        loading: false 
      };
    
    case ActionTypes.SET_ITEMS:
      return { ...state, items: action.payload, loading: false };
    
    case ActionTypes.ADD_ITEM:
      return { 
        ...state, 
        items: [action.payload, ...state.items],
        loading: false 
      };
    
    case ActionTypes.SET_FILTERS:
      return { ...state, filters: { ...state.filters, ...action.payload } };
    
    case ActionTypes.SET_VIEW_MODE:
      return { ...state, viewMode: action.payload };
    
    case ActionTypes.SET_EXTENSION_CONTEXT:
      return { ...state, isExtensionContext: action.payload };
    
    case ActionTypes.SET_NETWORK_STATUS:
      return { ...state, networkStatus: action.payload };
    
    case ActionTypes.SET_SETTINGS:
      return { ...state, settings: { ...state.settings, ...action.payload } };
    
    // Creation flow cases
    case ActionTypes.UPDATE_CREATION_DATA:
      return {
        ...state,
        creationData: {
          ...state.creationData,
          [action.payload.section]: {
            ...state.creationData[action.payload.section],
            ...action.payload.data
          }
        }
      };
    
    case ActionTypes.SET_CREATION_STEP:
      return { ...state, creationStep: action.payload };
    
    case ActionTypes.UPDATE_CREATION_PROGRESS:
      return {
        ...state,
        creationProgress: { ...state.creationProgress, ...action.payload }
      };
    
    case ActionTypes.RESET_CREATION_DATA:
      return {
        ...state,
        creationData: initialState.creationData,
        creationStep: 'basicInfo',
        creationProgress: initialState.creationProgress
      };
    
    case ActionTypes.SET_MODEL_FILE:
      return {
        ...state,
        creationData: {
          ...state.creationData,
          modelFile: action.payload.file,
          modelUrl: action.payload.url
        }
      };
    
    case ActionTypes.SET_ANIMATIONS:
      return {
        ...state,
        creationData: {
          ...state.creationData,
          animations: action.payload.animations,
          selectedAnimations: action.payload.selected || state.creationData.selectedAnimations
        }
      };
    
    case ActionTypes.UPDATE_MARKETING_DATA:
      return {
        ...state,
        creationData: {
          ...state.creationData,
          marketingData: { ...state.creationData.marketingData, ...action.payload }
        }
      };

    case ActionTypes.UPDATE_ANIMATION_CUSTOMIZATIONS:
      return {
        ...state,
        creationData: {
          ...state.creationData,
          animationCustomizations: { ...state.creationData.animationCustomizations, ...action.payload }
        }
      };

    default:
      return state;
  }
};

// Create context
const DexHeroContext = createContext();

// Provider component
export const DexHeroProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dexheroReducer, initialState);

  // Initialize extension context and handle session management
  useEffect(() => {
    const initExtension = async () => {
      // Clear creation data on new browser session
      const sessionId = sessionStorage.getItem('dexhero-session-id');
      const currentSessionId = Date.now().toString();
      
      if (!sessionId || sessionId !== currentSessionId) {
        // New session detected - clear any persisted creation data
        localStorage.removeItem('dexhero-creation-draft');
        sessionStorage.setItem('dexhero-session-id', currentSessionId);
        console.log('New session detected - cleared creation draft');
      }
      
      const isExtension = await initializeExtensionContext();
      dispatch({ type: ActionTypes.SET_EXTENSION_CONTEXT, payload: isExtension });
      
      if (isExtension) {
        // Load settings from extension storage
        const settings = await ChromeExtensionService.getSettings();
        dispatch({ type: ActionTypes.SET_SETTINGS, payload: settings });
        
        // Load cached data
        const cachedDexHeroes = await ChromeExtensionService.getCachedDexHeroes();
        if (cachedDexHeroes.length > 0) {
          dispatch({ type: ActionTypes.SET_DEXHEROES, payload: cachedDexHeroes });
        }
      }
    };

    initExtension();
  }, []);

  // Set up extension event listeners
  useEffect(() => {
    if (state.isExtensionContext) {
      const handleSyncRequest = () => {
        // Trigger sync will be handled by the actions object
        console.log('Sync requested from background');
      };

      const handleNetworkStatus = (event) => {
        dispatch({ type: ActionTypes.SET_NETWORK_STATUS, payload: event.detail.online });
      };

      const handleStorageChange = (event) => {
        console.log('Storage changed:', event.detail);
        // Reload data if needed
      };

      window.addEventListener('dexhero-sync-requested', handleSyncRequest);
      window.addEventListener('dexhero-network-status', handleNetworkStatus);
      window.addEventListener('dexhero-storage-changed', handleStorageChange);

      return () => {
        window.removeEventListener('dexhero-sync-requested', handleSyncRequest);
        window.removeEventListener('dexhero-network-status', handleNetworkStatus);
        window.removeEventListener('dexhero-storage-changed', handleStorageChange);
      };
    }
  }, [state.isExtensionContext]);

  // Actions
  const actions = {
    // DexHero actions
    async loadDexHeroes(filters = {}) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const dexheroes = await DexHeroService.getDexHeroes(filters);
        dispatch({ type: ActionTypes.SET_DEXHEROES, payload: dexheroes });
        
        // Cache in extension storage
        if (state.isExtensionContext) {
          await ChromeExtensionService.setCachedDexHeroes(dexheroes);
          await ChromeExtensionService.syncDexHeroes(dexheroes);
        }
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    async createDexHero(dexheroData, glbFile) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const newDexHero = await DexHeroService.createDexHero(dexheroData, glbFile);
        dispatch({ type: ActionTypes.ADD_DEXHERO, payload: newDexHero });
        
        if (state.isExtensionContext) {
          await ChromeExtensionService.showNotification(
            'DexHero Created!',
            `${newDexHero.name} has been successfully created.`
          );
        }
        
        return newDexHero;
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    async updateDexHero(id, updates) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const updatedDexHero = await DexHeroService.updateDexHero(id, updates);
        dispatch({ type: ActionTypes.UPDATE_DEXHERO, payload: updatedDexHero });
        return updatedDexHero;
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    async deleteDexHero(id) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        await DexHeroService.deleteDexHero(id);
        dispatch({ type: ActionTypes.DELETE_DEXHERO, payload: id });
        
        if (state.isExtensionContext) {
          await ChromeExtensionService.showNotification(
            'DexHero Deleted',
            'DexHero has been successfully deleted.'
          );
        }
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Game actions
    async loadGames(filters = {}) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const games = await GameService.getGames(filters);
        dispatch({ type: ActionTypes.SET_GAMES, payload: games });
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    async createGame(gameData) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const newGame = await GameService.createGame(gameData);
        dispatch({ type: ActionTypes.ADD_GAME, payload: newGame });
        return newGame;
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Item actions
    async loadItems(filters = {}) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const items = await ItemService.getItems(filters);
        dispatch({ type: ActionTypes.SET_ITEMS, payload: items });
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    async createItem(itemData) {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      try {
        const newItem = await ItemService.createItem(itemData);
        dispatch({ type: ActionTypes.ADD_ITEM, payload: newItem });
        return newItem;
      } catch (error) {
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Filter and view actions
    setFilters(filters) {
      dispatch({ type: ActionTypes.SET_FILTERS, payload: filters });
    },

    setViewMode(mode) {
      dispatch({ type: ActionTypes.SET_VIEW_MODE, payload: mode });
    },

    // Settings actions
    async updateSettings(newSettings) {
      dispatch({ type: ActionTypes.SET_SETTINGS, payload: newSettings });
      
      if (state.isExtensionContext) {
        await ChromeExtensionService.updateSettings({ ...state.settings, ...newSettings });
      }
    },

    // Utility actions
    clearError() {
      dispatch({ type: ActionTypes.SET_ERROR, payload: null });
    },

    async syncData() {
      if (state.isExtensionContext) {
        await actions.loadDexHeroes();
        await actions.loadGames();
        await actions.loadItems();
      }
    },

    // Creation flow actions
    updateCreationData(section, data) {
      dispatch({ 
        type: ActionTypes.UPDATE_CREATION_DATA, 
        payload: { section, data } 
      });
      
      // Auto-save to localStorage
      const currentData = { ...state.creationData };
      currentData[section] = { ...currentData[section], ...data };
      localStorage.setItem('dexhero-creation-draft', JSON.stringify(currentData));
    },

    setCreationStep(step) {
      dispatch({ type: ActionTypes.SET_CREATION_STEP, payload: step });
    },

    updateCreationProgress(progress) {
      dispatch({ type: ActionTypes.UPDATE_CREATION_PROGRESS, payload: progress });
    },

    resetCreationData() {
      dispatch({ type: ActionTypes.RESET_CREATION_DATA });
      localStorage.removeItem('dexhero-creation-draft');
    },

    setModelFile(file, url) {
      dispatch({ 
        type: ActionTypes.SET_MODEL_FILE, 
        payload: { file, url } 
      });
    },

    setAnimations(animations, selected = []) {
      dispatch({ 
        type: ActionTypes.SET_ANIMATIONS, 
        payload: { animations, selected } 
      });
      
      // Auto-save to localStorage including custom icon data
      const currentData = { ...state.creationData };
      currentData.animations = animations;
      currentData.selectedAnimations = selected;
      localStorage.setItem('dexhero-creation-draft', JSON.stringify(currentData));
    },

    updateMarketingData(data) {
      dispatch({ type: ActionTypes.UPDATE_MARKETING_DATA, payload: data });
    },

    updateAnimationCustomizations(customizations) {
      dispatch({ type: ActionTypes.UPDATE_ANIMATION_CUSTOMIZATIONS, payload: customizations });

      // Auto-save to localStorage
      const currentData = { ...state.creationData };
      currentData.animationCustomizations = { ...currentData.animationCustomizations, ...customizations };
      localStorage.setItem('dexhero-creation-draft', JSON.stringify(currentData));
    },

    // Validation helpers
    validateBasicInfo() {
      const { name, blockchain, ticker, description } = state.creationData.basicInfo;
      const isValid = name.trim() && blockchain && ticker.trim() && description.trim();
      
      if (isValid) {
        dispatch({ 
          type: ActionTypes.UPDATE_CREATION_PROGRESS, 
          payload: { basicInfo: true } 
        });
      }
      
      return isValid;
    },

    validateModelUpload() {
      const isValid = state.creationData.modelFile && state.creationData.modelUrl;
      
      if (isValid) {
        dispatch({ 
          type: ActionTypes.UPDATE_CREATION_PROGRESS, 
          payload: { modelUpload: true } 
        });
      }
      
      return isValid;
    },

    validateAnimations() {
      const isValid = state.creationData.animations.length > 0;
      
      if (isValid) {
        dispatch({ 
          type: ActionTypes.UPDATE_CREATION_PROGRESS, 
          payload: { animations: true } 
        });
      }
      
      return isValid;
    },

    validateMarketing() {
      const isValid = true; // Marketing is always valid as it's just positioning
      
      if (isValid) {
        dispatch({ 
          type: ActionTypes.UPDATE_CREATION_PROGRESS, 
          payload: { marketing: true } 
        });
      }
      
      return isValid;
    },

    // Auto-generate ticker from name
    generateTicker(name) {
      if (!name) return '';
      
      // Remove special characters and spaces, take first 4-6 characters
      const ticker = name
        .replace(/[^a-zA-Z0-9]/g, '')
        .toUpperCase()
        .substring(0, 6);
      
      return ticker;
    },

    // Load draft from localStorage
    loadCreationDraft() {
      try {
        const draft = localStorage.getItem('dexhero-creation-draft');
        if (draft) {
          const parsedDraft = JSON.parse(draft);
          dispatch({ 
            type: ActionTypes.UPDATE_CREATION_DATA, 
            payload: { section: 'basicInfo', data: parsedDraft.basicInfo || {} }
          });
          dispatch({ 
            type: ActionTypes.UPDATE_CREATION_DATA, 
            payload: { section: 'marketingData', data: parsedDraft.marketingData || {} }
          });
          
          if (parsedDraft.modelUrl) {
            dispatch({ 
              type: ActionTypes.SET_MODEL_FILE, 
              payload: { file: null, url: parsedDraft.modelUrl } 
            });
          }
          
          if (parsedDraft.animations) {
            dispatch({
              type: ActionTypes.SET_ANIMATIONS,
              payload: {
                animations: parsedDraft.animations,
                selected: parsedDraft.selectedAnimations || []
              }
            });
          }

          if (parsedDraft.animationCustomizations) {
            dispatch({
              type: ActionTypes.UPDATE_ANIMATION_CUSTOMIZATIONS,
              payload: parsedDraft.animationCustomizations
            });
          }
        }
      } catch (error) {
        console.error('Failed to load creation draft:', error);
      }
    }
  };

  // Filter data based on current filters
  const getFilteredData = () => {
    let data = [];
    
    switch (state.filters.type) {
      case 'dexheroes':
        data = state.dexheroes;
        break;
      case 'games':
        data = state.games;
        break;
      case 'items':
        data = state.items;
        break;
      default:
        data = [...state.dexheroes, ...state.games, ...state.items];
    }

    // Apply filters
    if (state.filters.blockchain) {
      data = data.filter(item => 
        item.metadata?.blockchain === state.filters.blockchain ||
        item.blockchain === state.filters.blockchain
      );
    }

    if (state.filters.creator) {
      data = data.filter(item => 
        item.metadata?.creator === state.filters.creator ||
        item.creator === state.filters.creator
      );
    }

    if (state.filters.tags && state.filters.tags.length > 0) {
      data = data.filter(item => 
        item.tags && item.tags.some(tag => state.filters.tags.includes(tag))
      );
    }

    return data;
  };

  const value = {
    ...state,
    ...actions,
    filteredData: getFilteredData()
  };

  return (
    <DexHeroContext.Provider value={value}>
      {children}
    </DexHeroContext.Provider>
  );
};

// Custom hook to use the context
export const useDexHero = () => {
  const context = useContext(DexHeroContext);
  if (!context) {
    throw new Error('useDexHero must be used within a DexHeroProvider');
  }
  return context;
};

export default DexHeroContext;
