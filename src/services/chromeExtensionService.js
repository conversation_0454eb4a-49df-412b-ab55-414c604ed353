// Chrome Extension Service for DexHero
// Handles browser extension specific functionality

export class ChromeExtensionService {
  // Check if running in extension context
  static isExtensionContext() {
    return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
  }

  // Storage operations
  static async getStorageData(keys) {
    if (!this.isExtensionContext()) {
      return localStorage.getItem(keys) ? JSON.parse(localStorage.getItem(keys)) : null;
    }

    return new Promise((resolve) => {
      chrome.storage.local.get(keys, (result) => {
        resolve(result);
      });
    });
  }

  static async setStorageData(data) {
    if (!this.isExtensionContext()) {
      Object.keys(data).forEach(key => {
        localStorage.setItem(key, JSON.stringify(data[key]));
      });
      return;
    }

    return new Promise((resolve) => {
      chrome.storage.local.set(data, () => {
        resolve();
      });
    });
  }

  static async clearStorage() {
    if (!this.isExtensionContext()) {
      localStorage.clear();
      return;
    }

    return new Promise((resolve) => {
      chrome.storage.local.clear(() => {
        resolve();
      });
    });
  }

  // Message passing
  static async sendMessage(message) {
    if (!this.isExtensionContext()) {
      console.log('Message would be sent:', message);
      return { success: true };
    }

    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response);
      });
    });
  }

  // Background sync operations
  static async syncDexHeroes(dexheroes) {
    const message = {
      action: 'syncDexHeroes',
      data: {
        items: dexheroes,
        timestamp: Date.now()
      }
    };

    return await this.sendMessage(message);
  }

  // Settings management
  static async getSettings() {
    const data = await this.getStorageData(['dexheroSettings']);
    return data.dexheroSettings || {
      autoSync: true,
      notifications: true,
      defaultBlockchain: 'ethereum'
    };
  }

  static async updateSettings(settings) {
    await this.setStorageData({ dexheroSettings: settings });
  }

  // Cache management
  static async getCachedDexHeroes() {
    const data = await this.getStorageData(['cachedDexHeroes']);
    return data.cachedDexHeroes || [];
  }

  static async setCachedDexHeroes(dexheroes) {
    await this.setStorageData({ 
      cachedDexHeroes: dexheroes,
      lastCacheUpdate: Date.now()
    });
  }

  // Network status
  static async getNetworkStatus() {
    if (!this.isExtensionContext()) {
      return navigator.onLine;
    }

    // In extension context, we rely on background script monitoring
    const data = await this.getStorageData(['networkStatus']);
    return data.networkStatus !== false; // Default to true if not set
  }

  // Notifications
  static async showNotification(title, message, iconUrl = 'icons/icon-48.png') {
    if (!this.isExtensionContext()) {
      console.log(`Notification: ${title} - ${message}`);
      return;
    }

    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl,
        title,
        message
      });
    }
  }

  // Tab management
  static async openTab(url) {
    if (!this.isExtensionContext()) {
      window.open(url, '_blank');
      return;
    }

    chrome.tabs.create({ url });
  }

  // Extension lifecycle
  static async getExtensionInfo() {
    if (!this.isExtensionContext()) {
      return {
        version: '1.0.0',
        name: 'DexHero (Web Version)'
      };
    }

    const manifest = chrome.runtime.getManifest();
    return {
      version: manifest.version,
      name: manifest.name,
      id: chrome.runtime.id
    };
  }

  // Event listeners for extension context
  static addMessageListener(callback) {
    if (!this.isExtensionContext()) {
      return;
    }

    chrome.runtime.onMessage.addListener(callback);
  }

  static addStorageListener(callback) {
    if (!this.isExtensionContext()) {
      return;
    }

    chrome.storage.onChanged.addListener(callback);
  }

  // Utility methods
  static async isFirstRun() {
    const data = await this.getStorageData(['hasRunBefore']);
    return !data.hasRunBefore;
  }

  static async markAsRun() {
    await this.setStorageData({ hasRunBefore: true });
  }

  // Backup and restore
  static async exportData() {
    const data = await this.getStorageData(null); // Get all data
    return {
      exportDate: new Date().toISOString(),
      version: '1.0.0',
      data
    };
  }

  static async importData(exportedData) {
    if (!exportedData.data) {
      throw new Error('Invalid export data format');
    }

    await this.setStorageData(exportedData.data);
    return true;
  }

  // Performance monitoring
  static async getPerformanceMetrics() {
    const data = await this.getStorageData(['performanceMetrics']);
    return data.performanceMetrics || {
      syncCount: 0,
      lastSyncDuration: 0,
      averageSyncDuration: 0,
      errorCount: 0
    };
  }

  static async updatePerformanceMetrics(metrics) {
    const current = await this.getPerformanceMetrics();
    const updated = { ...current, ...metrics };
    
    // Calculate average sync duration
    if (metrics.lastSyncDuration) {
      updated.syncCount = (current.syncCount || 0) + 1;
      updated.averageSyncDuration = (
        ((current.averageSyncDuration || 0) * (updated.syncCount - 1) + metrics.lastSyncDuration) / 
        updated.syncCount
      );
    }

    await this.setStorageData({ performanceMetrics: updated });
  }
}

// Extension context detection and initialization
export const initializeExtensionContext = async () => {
  const isExtension = ChromeExtensionService.isExtensionContext();
  
  if (isExtension) {
    console.log('Running in Chrome Extension context');
    
    // Set up message listeners
    ChromeExtensionService.addMessageListener((message, sender, sendResponse) => {
      console.log('Popup received message:', message);
      
      switch (message.action) {
        case 'performSync':
          // Trigger sync in the UI
          window.dispatchEvent(new CustomEvent('dexhero-sync-requested'));
          break;
          
        case 'networkStatusChanged':
          // Update network status in UI
          window.dispatchEvent(new CustomEvent('dexhero-network-status', {
            detail: { online: message.online }
          }));
          break;
          
        case 'storageChanged':
          // Handle storage changes
          window.dispatchEvent(new CustomEvent('dexhero-storage-changed', {
            detail: message.changes
          }));
          break;
      }
    });

    // Check if this is the first run
    const isFirstRun = await ChromeExtensionService.isFirstRun();
    if (isFirstRun) {
      console.log('First run detected');
      await ChromeExtensionService.markAsRun();
      
      // Show welcome notification
      await ChromeExtensionService.showNotification(
        'Welcome to DexHero!',
        'Your asset manager extension is ready to use.'
      );
    }
  } else {
    console.log('Running in web context');
  }
  
  return isExtension;
};
