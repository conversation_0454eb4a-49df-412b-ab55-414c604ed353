import { 
  collection, 
  doc, 
  addDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';
import { db, storage } from '../config/firebase';
import { v4 as uuidv4 } from 'uuid';

// Collections
const DEXHEROES_COLLECTION = 'dexheroes';
const GAMES_COLLECTION = 'games';
const ITEMS_COLLECTION = 'items';

// DexHero Service
export class DexHeroService {
  // Create a new DexHero
  static async createDexHero(dexheroData, glbFile) {
    try {
      const dexheroId = uuidv4();
      
      // Upload GLB file to Firebase Storage
      let glbUrl = null;
      if (glbFile) {
        const glbRef = ref(storage, `dexheroes/${dexheroId}/model.glb`);
        const snapshot = await uploadBytes(glbRef, glbFile);
        glbUrl = await getDownloadURL(snapshot.ref);
      }

      // Create DexHero document
      const dexhero = {
        id: dexheroId,
        name: dexheroData.name,
        description: dexheroData.description,
        glbUrl,
        imageUrl: dexheroData.imageUrl || null,
        metadata: {
          creator: dexheroData.creator,
          blockchain: dexheroData.blockchain,
          tokenSupply: 1000000000, // 1 Billion fixed supply
          soulboundNFT: {
            tokenId: null, // Will be set when NFT is minted
            contractAddress: null
          },
          characterToken: {
            tokenAddress: null, // Will be set when token is deployed
            symbol: dexheroData.tokenSymbol
          }
        },
        animations: dexheroData.animations || [],
        stats: dexheroData.stats || {},
        tags: dexheroData.tags || [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };

      const docRef = await addDoc(collection(db, DEXHEROES_COLLECTION), dexhero);
      return { id: docRef.id, ...dexhero };
    } catch (error) {
      console.error('Error creating DexHero:', error);
      throw error;
    }
  }

  // Get DexHero by ID
  static async getDexHero(id) {
    try {
      const docRef = doc(db, DEXHEROES_COLLECTION, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        throw new Error('DexHero not found');
      }
    } catch (error) {
      console.error('Error getting DexHero:', error);
      throw error;
    }
  }

  // Get all DexHeroes with optional filters
  static async getDexHeroes(filters = {}) {
    try {
      let q = collection(db, DEXHEROES_COLLECTION);
      
      // Apply filters
      if (filters.creator) {
        q = query(q, where('metadata.creator', '==', filters.creator));
      }
      
      if (filters.blockchain) {
        q = query(q, where('metadata.blockchain', '==', filters.blockchain));
      }
      
      if (filters.tags && filters.tags.length > 0) {
        q = query(q, where('tags', 'array-contains-any', filters.tags));
      }
      
      // Add ordering
      q = query(q, orderBy('createdAt', 'desc'));
      
      // Add limit if specified
      if (filters.limit) {
        q = query(q, limit(filters.limit));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting DexHeroes:', error);
      throw error;
    }
  }

  // Update DexHero
  static async updateDexHero(id, updates) {
    try {
      const docRef = doc(db, DEXHEROES_COLLECTION, id);
      const updateData = {
        ...updates,
        updatedAt: new Date()
      };
      
      await updateDoc(docRef, updateData);
      return await this.getDexHero(id);
    } catch (error) {
      console.error('Error updating DexHero:', error);
      throw error;
    }
  }

  // Delete DexHero
  static async deleteDexHero(id) {
    try {
      // Get DexHero data first to clean up storage
      const dexhero = await this.getDexHero(id);
      
      // Delete GLB file from storage if exists
      if (dexhero.glbUrl) {
        const glbRef = ref(storage, `dexheroes/${id}/model.glb`);
        await deleteObject(glbRef);
      }
      
      // Delete document
      const docRef = doc(db, DEXHEROES_COLLECTION, id);
      await deleteDoc(docRef);
      
      return true;
    } catch (error) {
      console.error('Error deleting DexHero:', error);
      throw error;
    }
  }

  // Upload additional assets (images, animations, etc.)
  static async uploadAsset(dexheroId, file, assetType) {
    try {
      const fileName = `${assetType}_${Date.now()}_${file.name}`;
      const assetRef = ref(storage, `dexheroes/${dexheroId}/assets/${fileName}`);
      
      const snapshot = await uploadBytes(assetRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return {
        url: downloadURL,
        fileName,
        type: assetType,
        uploadedAt: new Date()
      };
    } catch (error) {
      console.error('Error uploading asset:', error);
      throw error;
    }
  }
}

// Game Service
export class GameService {
  static async createGame(gameData) {
    try {
      const game = {
        ...gameData,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };
      
      const docRef = await addDoc(collection(db, GAMES_COLLECTION), game);
      return { id: docRef.id, ...game };
    } catch (error) {
      console.error('Error creating game:', error);
      throw error;
    }
  }

  static async getGames(filters = {}) {
    try {
      let q = collection(db, GAMES_COLLECTION);
      
      if (filters.category) {
        q = query(q, where('category', '==', filters.category));
      }
      
      q = query(q, orderBy('createdAt', 'desc'));
      
      if (filters.limit) {
        q = query(q, limit(filters.limit));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting games:', error);
      throw error;
    }
  }
}

// Item Service
export class ItemService {
  static async createItem(itemData) {
    try {
      const item = {
        ...itemData,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };
      
      const docRef = await addDoc(collection(db, ITEMS_COLLECTION), item);
      return { id: docRef.id, ...item };
    } catch (error) {
      console.error('Error creating item:', error);
      throw error;
    }
  }

  static async getItems(filters = {}) {
    try {
      let q = collection(db, ITEMS_COLLECTION);
      
      if (filters.type) {
        q = query(q, where('type', '==', filters.type));
      }
      
      if (filters.rarity) {
        q = query(q, where('rarity', '==', filters.rarity));
      }
      
      q = query(q, orderBy('createdAt', 'desc'));
      
      if (filters.limit) {
        q = query(q, limit(filters.limit));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting items:', error);
      throw error;
    }
  }
}
