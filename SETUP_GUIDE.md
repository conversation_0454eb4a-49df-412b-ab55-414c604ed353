# DexHero Setup Guide

This guide will help you set up the DexHero browser extension with Firebase integration.

## Prerequisites

- Node.js 16+ installed
- Chrome browser for testing
- Firebase account
- Basic knowledge of React and Firebase

## Step-by-Step Setup

### 1. <PERSON>lone and Install

```bash
# Navigate to project directory
cd dexhero_main_-_homeitemlist_pd3f2h\ 2

# Install dependencies (already done)
npm install
```

### 2. Firebase Project Setup

#### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `dexhero-extension`
4. Enable Google Analytics (optional)
5. Create project

#### Enable Required Services
1. **Firestore Database**:
   - Go to Firestore Database
   - Click "Create database"
   - Choose "Start in test mode"
   - Select location closest to your users

2. **Storage**:
   - Go to Storage
   - Click "Get started"
   - Choose "Start in test mode"
   - Keep default location

3. **Authentication** (Optional):
   - Go to Authentication
   - Click "Get started"
   - Enable desired sign-in methods

#### Get Firebase Configuration
1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon to add web app
4. Register app with name: `DexHero Extension`
5. Copy the Firebase configuration object

### 3. Configure Environment Variables

Create a `.env` file in the project root:

```env
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your-api-key-here
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

**OR** update `src/config/firebase.js` directly:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

### 4. Add Extension Icons

Add these icon files to `public/icons/`:
- `icon-16.png` (16x16 pixels)
- `icon-32.png` (32x32 pixels)
- `icon-48.png` (48x48 pixels)
- `icon-128.png` (128x128 pixels)

You can create simple icons or use online icon generators.

### 5. Development Testing

#### Test Web Version
```bash
npm run dev
```
Visit `http://localhost:5173` to test the web version.

#### Test Extension Version
```bash
# Build extension
npm run build:extension

# Load in Chrome:
# 1. Open chrome://extensions/
# 2. Enable "Developer mode"
# 3. Click "Load unpacked"
# 4. Select the "dist-extension" folder
```

### 6. Firebase Security Rules

#### Firestore Rules
Update Firestore rules for development:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write for development
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

#### Storage Rules
Update Storage rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

**⚠️ Important**: These rules are for development only. Implement proper security rules for production.

### 7. Testing the Integration

#### Test DexHero Creation
1. Open the extension or web app
2. Navigate to Create section
3. Fill in character details
4. Upload a .glb file (optional for testing)
5. Submit the form
6. Check Firebase Console to see the data

#### Test Data Retrieval
1. Navigate to the home screen
2. Verify characters are displayed
3. Test filtering functionality
4. Check browser extension storage

### 8. Production Deployment

#### Web Deployment
```bash
npm run build
# Deploy 'dist' folder to your hosting service
```

#### Extension Store
```bash
npm run build:extension
# Zip 'dist-extension' folder
# Upload to Chrome Web Store
```

## Troubleshooting

### Common Issues

1. **Firebase Connection Error**:
   - Verify API keys are correct
   - Check network connectivity
   - Ensure Firebase services are enabled

2. **Extension Not Loading**:
   - Check manifest.json syntax
   - Verify all required files are present
   - Check Chrome developer console for errors

3. **Build Errors**:
   - Clear node_modules and reinstall
   - Check for TypeScript errors
   - Verify all imports are correct

### Debug Tips

1. **Check Browser Console**:
   - Open DevTools (F12)
   - Look for error messages
   - Check Network tab for failed requests

2. **Firebase Debug**:
   - Enable Firebase debug mode
   - Check Firestore/Storage usage in console
   - Verify security rules

3. **Extension Debug**:
   - Go to chrome://extensions/
   - Click "Inspect views: background page"
   - Check service worker console

## Next Steps

1. Customize the UI to match your brand
2. Implement blockchain integration
3. Add user authentication
4. Set up proper Firebase security rules
5. Add more advanced features

## Support

- Check the main README.md for detailed documentation
- Review Firebase documentation
- Test in both web and extension modes
- Create issues for bugs or feature requests

---

Happy building! 🚀
