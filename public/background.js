// Background service worker for DexHero extension

// Extension installation/update handler
chrome.runtime.onInstalled.addListener((details) => {
  console.log('DexHero extension installed/updated:', details.reason);
  
  // Set default storage values
  chrome.storage.local.set({
    dexheroSettings: {
      autoSync: true,
      notifications: true,
      defaultBlockchain: 'ethereum'
    }
  });
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Open the popup (this is handled automatically by manifest.json)
  console.log('DexHero extension clicked');
});

// Message handling between content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'syncDexHeroes':
      handleDexHeroSync(request.data)
        .then(result => sendResponse({ success: true, data: result }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Keep message channel open for async response
      
    case 'getStorageData':
      chrome.storage.local.get(request.keys, (result) => {
        sendResponse({ success: true, data: result });
      });
      return true;
      
    case 'setStorageData':
      chrome.storage.local.set(request.data, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'clearCache':
      chrome.storage.local.clear(() => {
        sendResponse({ success: true });
      });
      return true;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// Handle DexHero synchronization
async function handleDexHeroSync(data) {
  try {
    // Store sync timestamp
    const syncData = {
      lastSync: Date.now(),
      syncedItems: data.items || [],
      syncStatus: 'completed'
    };
    
    await chrome.storage.local.set({ lastDexHeroSync: syncData });
    
    // Trigger notification if enabled
    const settings = await chrome.storage.local.get('dexheroSettings');
    if (settings.dexheroSettings?.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'DexHero Sync Complete',
        message: `Synchronized ${data.items?.length || 0} items`
      });
    }
    
    return syncData;
  } catch (error) {
    console.error('Sync error:', error);
    throw error;
  }
}

// Periodic sync (every 30 minutes)
chrome.alarms.create('dexheroSync', { periodInMinutes: 30 });

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'dexheroSync') {
    console.log('Performing periodic DexHero sync');
    // Trigger sync in popup if it's open
    chrome.runtime.sendMessage({ action: 'performSync' });
  }
});

// Handle storage changes
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log('Storage changed:', changes, namespace);
  
  // Notify popup about storage changes
  chrome.runtime.sendMessage({
    action: 'storageChanged',
    changes: changes,
    namespace: namespace
  });
});

// Network status monitoring
let isOnline = true;

// Check network status periodically
setInterval(() => {
  fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' })
    .then(() => {
      if (!isOnline) {
        isOnline = true;
        chrome.runtime.sendMessage({ action: 'networkStatusChanged', online: true });
      }
    })
    .catch(() => {
      if (isOnline) {
        isOnline = false;
        chrome.runtime.sendMessage({ action: 'networkStatusChanged', online: false });
      }
    });
}, 30000); // Check every 30 seconds

// Error handling
self.addEventListener('error', (event) => {
  console.error('Background script error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});
