{"manifest_version": 3, "name": "DexHero", "version": "1.0.0", "description": "Asset manager browser extension for tokenized avatars, games and items", "permissions": ["storage", "activeTab", "scripting"], "host_permissions": ["https://*.firebaseapp.com/*", "https://*.googleapis.com/*"], "action": {"default_popup": "index.html", "default_title": "DexHero", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "background": {"service_worker": "background.js"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["assets/*"], "matches": ["<all_urls>"]}]}