# Git Guide for DexHero Project

## Current Status
✅ Git repository initialized
✅ Initial commit created with all project files
✅ .gitignore configured to exclude unnecessary files

## Basic Git Commands for This Project

### Check Status
```bash
git status
```
Shows which files have been modified, added, or deleted.

### Add Changes
```bash
# Add specific file
git add src/screens/Marketingpositioning/Marketingpositioning.jsx

# Add all changes
git add .

# Add all files in a directory
git add src/screens/
```

### Commit Changes
```bash
# Commit with message
git commit -m "Add new feature: marketing preview enhancement"

# Commit with detailed message
git commit -m "Fix: Marketing page model upload check

- Added conditional rendering for no model state
- Improved user experience with clear messaging
- Maintained existing functionality when model is present"
```

### View History
```bash
# View commit history (one line per commit)
git log --oneline

# View detailed commit history
git log

# View changes in a specific commit
git show a9fd443
```

### Branch Management
```bash
# Create new branch
git checkout -b feature/new-animation-system

# Switch between branches
git checkout main
git checkout feature/new-animation-system

# List all branches
git branch
```

### Useful Commands for Development

#### Before Making Changes
```bash
git status  # Check current state
git pull    # Get latest changes (if working with remote)
```

#### After Making Changes
```bash
git add .                           # Stage all changes
git commit -m "Descriptive message" # Commit changes
git push                           # Push to remote (if configured)
```

#### View Changes
```bash
# See what changed in files
git diff

# See what's staged for commit
git diff --staged

# See changes in a specific file
git diff src/screens/Marketingpositioning/Marketingpositioning.jsx
```

## Recommended Workflow

### For New Features
1. Create a new branch: `git checkout -b feature/feature-name`
2. Make your changes
3. Add and commit: `git add . && git commit -m "Add feature description"`
4. Switch back to main: `git checkout main`
5. Merge the feature: `git merge feature/feature-name`

### For Bug Fixes
1. Create a new branch: `git checkout -b fix/bug-description`
2. Fix the bug
3. Add and commit: `git add . && git commit -m "Fix: bug description"`
4. Switch back to main: `git checkout main`
5. Merge the fix: `git merge fix/bug-description`

### Daily Development
```bash
# Start of day
git status
git pull  # If working with remote repository

# During development (commit frequently)
git add .
git commit -m "Work in progress: feature description"

# End of day
git add .
git commit -m "Complete: feature description"
git push  # If working with remote repository
```

## Setting Up Remote Repository (Optional)

### GitHub
1. Create a new repository on GitHub
2. Add remote origin:
```bash
git remote add origin https://github.com/yourusername/dexheroclaud.git
git branch -M main
git push -u origin main
```

### GitLab
1. Create a new repository on GitLab
2. Add remote origin:
```bash
git remote add origin https://gitlab.com/yourusername/dexheroclaud.git
git branch -M main
git push -u origin main
```

## Important Files in This Project

### Tracked Files (in Git)
- All source code in `src/`
- Configuration files (`package.json`, `vite.config.js`, etc.)
- Documentation (`README.md`, `SETUP_GUIDE.md`)
- Static assets in `public/`

### Ignored Files (not in Git)
- `node_modules/` - Dependencies (can be reinstalled)
- `dist/` and `dist-extension/` - Build outputs
- `.DS_Store` - macOS system files
- Environment files (`.env`)
- Logs and temporary files

## Backup Strategy
Your project is now safely stored in Git. To create backups:

1. **Local backup**: Copy the entire project folder
2. **Remote backup**: Push to GitHub/GitLab
3. **Multiple remotes**: Add multiple remote repositories

## Recovery
If you accidentally delete files:
```bash
# Restore a specific file
git checkout HEAD -- src/screens/Marketingpositioning/Marketingpositioning.jsx

# Restore all files to last commit
git reset --hard HEAD
```

## Next Steps
1. Consider setting up a remote repository (GitHub/GitLab)
2. Create branches for different features
3. Make regular commits as you develop
4. Use descriptive commit messages

Your DexHero project is now fully version controlled! 🎉
