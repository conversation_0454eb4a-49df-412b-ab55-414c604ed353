@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

:root {
  --blackampwhitewhite: rgba(255, 255, 255, 1);
  --gray-50: rgba(249, 250, 251, 1);
  --graygray-400: rgba(160, 174, 192, 1);
  --primarygreen500-success-color: rgba(1, 181, 116, 1);
  --warning-500: rgba(247, 144, 9, 1);
}

/* Hide scrollbars but keep functionality */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Ensure overflow elements still scroll */
.overflow-y-scroll, .overflow-x-scroll, .overflow-scroll {
  -webkit-overflow-scrolling: touch;
}
