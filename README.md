# DexHero - Playable NFT Gaming Platform

DexHero is a revolutionary web3 gaming platform that allows users to create, customize, and play with their own 3D NFT characters across multiple games and experiences.

## Features

### 🎮 Core Functionality
- **3D Model Upload**: Upload GLB/GLTF 3D models for your DexHero
- **Animation System**: Detect and configure animations from your 3D models
- **Marketing Preview**: See how your DexHero will appear across the platform
- **Token Integration**: Each DexHero comes with its own token
- **Cross-Game Compatibility**: Use your DexHero across supported games

### 🛠️ Technical Features
- React-based frontend with modern hooks
- 3D model rendering with model-viewer
- Firebase integration for data storage
- Chrome extension support
- Responsive design with Tailwind CSS

## Project Structure

```
dexheroclaud/
├── src/
│   ├── screens/           # Main application screens
│   │   ├── Create/        # DexHero creation flow
│   │   ├── Createbasicinfo/     # Basic info setup
│   │   ├── Createanimations/    # Animation configuration
│   │   ├── Marketingpositioning/ # Marketing preview
│   │   ├── Home*/         # Homepage variants
│   │   └── ...
│   ├── contexts/          # React contexts
│   ├── services/          # API and service layers
│   └── config/           # Configuration files
├── public/               # Static assets and extension files
├── dist-extension/       # Built extension files
└── static/              # Additional static files
```

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd dexheroclaud
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

### Building for Production

```bash
npm run build
```

## Usage

### Creating a DexHero

1. **Basic Info**: Enter your DexHero's name, ticker symbol, and description
2. **3D Model**: Upload a GLB file containing your 3D character model
3. **Animations**: Configure animations detected from your model
4. **Marketing**: Preview how your DexHero will appear across the platform
5. **Mint**: Complete the minting process to create your NFT

### Key Components

#### DexHero Context
The `DexHeroContext` manages the global state for DexHero creation:
- Basic information (name, ticker, description)
- 3D model data and URL
- Animation configurations
- Marketing settings
- Validation states

#### 3D Model Viewer
Uses Google's `model-viewer` component for:
- GLB/GLTF model rendering
- Animation playback
- Camera controls
- Auto-rotation

#### Animation System
- Automatic animation detection from GLB files
- Category-based organization (Movement, Combat, Idle, etc.)
- Custom icon support for animations
- Interactive animation preview

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Key Technologies

- **React 18** - Frontend framework
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Three.js** - 3D graphics (via model-viewer)
- **Firebase** - Backend services
- **React Router** - Client-side routing

## Chrome Extension

The project includes a Chrome extension for enhanced web3 integration:

1. Build the extension:
```bash
npm run build
```

2. Load the extension in Chrome:
   - Open Chrome Extensions (chrome://extensions/)
   - Enable Developer mode
   - Click "Load unpacked" and select the `dist-extension` folder

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Commit your changes: `git commit -m 'Add feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## File Structure Details

### Core Screens
- `Create.jsx` - Main creation flow orchestrator
- `Createbasicinfo.jsx` - Basic DexHero information input
- `Createanimations.jsx` - Animation detection and configuration
- `Marketingpositioning.jsx` - Marketing preview and confirmation

### Context Management
- `DexHeroContext.jsx` - Global state management for DexHero creation

### Services
- `dexheroService.js` - Core DexHero business logic
- `chromeExtensionService.js` - Chrome extension integration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue on GitHub or contact the development team.

---

Built with ❤️ for the web3 gaming community
