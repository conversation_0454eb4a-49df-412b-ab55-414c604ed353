import { screenGraphPlugin } from "@animaapp/vite-plugin-screen-graph";
import react from "@vitejs/plugin-react";
import tailwind from "tailwindcss";
import { defineConfig } from "vite";
import { resolve } from "path";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isExtension = mode === "extension";
  
  return {
    plugins: [react(), mode === "development" && screenGraphPlugin()],
    publicDir: isExtension ? "./public" : "./static",
    base: "./",
    css: {
      postcss: {
        plugins: [tailwind()],
      },
    },
    build: {
      outDir: isExtension ? "dist-extension" : "dist",
      rollupOptions: isExtension ? {
        input: {
          popup: resolve(__dirname, "index.html"),
          background: resolve(__dirname, "public/background.js")
        },
        output: {
          entryFileNames: (chunkInfo) => {
            return chunkInfo.name === 'background' ? 'background.js' : 'assets/[name]-[hash].js';
          }
        }
      } : undefined,
      target: isExtension ? "es2020" : "es2015",
    },
    define: {
      global: "globalThis",
    },
  };
});
